import numpy as np
import pandas as pd
from typing import List, Dict
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')


def extract_features_single_window(draws: List[List[int]], window_name: str = "") -> pd.DataFrame:
    """Create DataFrame with features for single time window.

    * rolling_miss_i   = miss rate of number i in last 3 rounds
    * decay_miss_i     = weighted miss with exponential decay
    * acceleration_i   = second derivative of miss rate
    """
    if len(draws) < 3:
        raise ValueError(f"draws must contain >= 3 rounds, got {len(draws)}")

    all_nums = range(1, 81)
    rounds = len(draws)

    # ma trận 0/1: 1 = tr<PERSON><PERSON><PERSON>, 0 = trúng
    miss_mat = np.ones((rounds, 80), dtype=int)
    for r, draw in enumerate(draws):
        for n in draw:
            if 1 <= n <= 80:
                miss_mat[r, n - 1] = 0

    feat_rows = {}
    for idx, num in enumerate(all_nums):
        col = miss_mat[:, idx]           # vector 0/1 độ dài rounds

        # --- rolling miss 3 kỳ cuối ---
        rolling_miss = col[-3:].mean()

        # --- decay weighted miss (kỳ mới quan trọng hơn) ---
        decay_weights = np.exp(-0.5 * np.arange(rounds)[::-1])
        decay_miss = np.sum(col * decay_weights)

        # --- miss acceleration (đạo hàm bậc 2) ---
        mr0 = col[-3:].mean()
        mr1 = col[-4:-1].mean() if rounds >= 4 else mr0
        mr2 = col[-5:-2].mean() if rounds >= 5 else mr1
        acceleration = mr0 - 2 * mr1 + mr2

        # Thêm đặc trưng nâng cao
        # Tần suất xuất hiện
        frequency = 1 - col.mean()
        
        # Độ biến thiên
        volatility = np.std(col) if rounds > 1 else 0
        
        # Khoảng cách từ lần xuất hiện cuối
        last_appearance = -1
        for i in range(rounds-1, -1, -1):
            if col[i] == 0:  # Xuất hiện
                last_appearance = rounds - 1 - i
                break
        if last_appearance == -1:
            last_appearance = rounds

        suffix = f"_{window_name}" if window_name else ""
        feat_rows[f"rolling_miss_{num}{suffix}"] = rolling_miss
        feat_rows[f"decay_miss_{num}{suffix}"] = decay_miss
        feat_rows[f"acceleration_{num}{suffix}"] = acceleration
        feat_rows[f"frequency_{num}{suffix}"] = frequency
        feat_rows[f"volatility_{num}{suffix}"] = volatility
        feat_rows[f"last_gap_{num}{suffix}"] = last_appearance

    return pd.DataFrame([feat_rows])


def analyze_block3_pattern(draws: List[List[int]]) -> Dict:
    """
    Phân tích mẫu block 3 kỳ theo yêu cầu:
    - Lấy 29 kỳ gần nhất + kỳ dự đoán = 30 kỳ
    - Chia thành 10 block 3 kỳ
    - Đánh số vị trí từ trái sang phải: 1, 2, 3 (kỳ dự đoán ở vị trí 3)
    """
    if len(draws) < 29:
        return {}
    
    # Lấy 29 kỳ gần nhất
    recent_29 = draws[-29:]
    
    # Tạo placeholder cho kỳ dự đoán (vị trí 3 trong block cuối)
    prediction_placeholder = []
    
    # Tổng 30 kỳ = 29 kỳ thực + 1 kỳ dự đoán
    all_30_draws = recent_29 + [prediction_placeholder]
    
    # Chia thành 10 block 3 kỳ
    patterns = {}
    
    for block_idx in range(10):
        start_idx = block_idx * 3
        block = all_30_draws[start_idx:start_idx + 3]
        
        for pos, draw in enumerate(block):
            position = pos + 1  # Vị trí 1, 2, 3
            
            for num in draw:
                if 1 <= num <= 80:
                    if num not in patterns:
                        patterns[num] = {
                            'positions': [0, 0, 0],  # Đếm số lần xuất hiện ở vị trí 1, 2, 3
                            'blocks': [],  # Lưu block nào xuất hiện
                            'total_count': 0
                        }
                    
                    patterns[num]['positions'][position - 1] += 1
                    patterns[num]['blocks'].append(block_idx)
                    patterns[num]['total_count'] += 1
    
    # Tính toán các chỉ số cho từng số
    result = {}
    for num in range(1, 81):
        if num not in patterns:
            patterns[num] = {
                'positions': [0, 0, 0],
                'blocks': [],
                'total_count': 0
            }
        
        pos_counts = patterns[num]['positions']
        total = patterns[num]['total_count']
        
        # Tính tỷ lệ xuất hiện ở vị trí 3 (vị trí dự đoán)
        pos3_ratio = pos_counts[2] / max(1, total)
        pos3_count = pos_counts[2]
        
        # Vị trí xuất hiện nhiều nhất
        dominant_position = np.argmax(pos_counts) + 1 if total > 0 else 0
        
        # Tần suất xuất hiện trong các block
        block_frequency = total / 10.0
        
        # Đặc trưng quan trọng: khả năng xuất hiện ở vị trí 3
        high_pos3_probability = 1 if pos3_count >= 3 else 0
        
        result[num] = {
            'pos3_ratio': pos3_ratio,
            'pos3_count': pos3_count,
            'dominant_position': dominant_position,
            'block_frequency': block_frequency,
            'high_pos3_probability': high_pos3_probability
        }
    
    return result


def extract_time_features(timestamp: str = None) -> Dict:
    """Trích xuất đặc trưng thời gian."""
    if not timestamp:
        dt = datetime.now()
    else:
        try:
            dt = datetime.strptime(timestamp, "%Y%m%d_%H%M%S")
        except:
            dt = datetime.now()
    
    return {
        'hour': dt.hour,
        'day_of_week': dt.weekday(),
        'is_morning': 1 if 6 <= dt.hour < 12 else 0,
        'is_afternoon': 1 if 12 <= dt.hour < 18 else 0,
        'is_evening': 1 if 18 <= dt.hour < 24 else 0,
        'is_night': 1 if 0 <= dt.hour < 6 else 0,
        'is_weekend': 1 if dt.weekday() >= 5 else 0,
        'time_period': get_time_period(dt.hour)
    }


def get_time_period(hour: int) -> str:
    """Phân loại thời gian trong ngày."""
    if 6 <= hour < 10:
        return 'early_morning'
    elif 10 <= hour < 14:
        return 'late_morning'
    elif 14 <= hour < 18:
        return 'afternoon'
    elif 18 <= hour < 22:
        return 'evening'
    else:
        return 'night'


def extract_enhanced_features(draws: List[List[int]], timestamp: str = None, min_draws: int = 30) -> pd.DataFrame:
    """
    Trích xuất đặc trưng nâng cao từ nhiều cửa sổ thời gian và block 3 kỳ pattern.
    """
    if len(draws) < min_draws:
        raise ValueError(f"draws must contain ≥ {min_draws} rounds, got {len(draws)}")
    
    # Các cửa sổ thời gian để bắt xu hướng ngắn hạn và dài hạn
    windows = []
    if len(draws) >= 3:
        windows.append(('w3', draws[-3:]))
    if len(draws) >= 12:
        windows.append(('w12', draws[-12:]))
    if len(draws) >= 30:
        windows.append(('w30', draws[-30:]))
    if len(draws) >= 70:
        windows.append(('w70', draws[-70:]))
    
    # Trích xuất đặc trưng cho từng cửa sổ
    feature_dfs = []
    for window_name, window_draws in windows:
        feat_df = extract_features_single_window(window_draws, window_name)
        feature_dfs.append(feat_df)
    
    # Đặc trưng block 3 kỳ nâng cao
    if len(draws) >= 29:
        block3_patterns = analyze_block3_pattern(draws)
        block3_features = {}
        
        for num in range(1, 81):
            pattern = block3_patterns[num]
            block3_features[f"block3_pos3_ratio_{num}"] = pattern['pos3_ratio']
            block3_features[f"block3_pos3_count_{num}"] = pattern['pos3_count']
            block3_features[f"block3_dominant_pos_{num}"] = pattern['dominant_position']
            block3_features[f"block3_frequency_{num}"] = pattern['block_frequency']
            block3_features[f"block3_high_pos3_{num}"] = pattern['high_pos3_probability']
        
        if block3_features:
            feature_dfs.append(pd.DataFrame([block3_features]))
    
    # Đặc trưng thời gian
    if timestamp:
        time_features = extract_time_features(timestamp)
        time_df = pd.DataFrame([{f"time_{k}": v for k, v in time_features.items() if k != 'time_period'}])
        feature_dfs.append(time_df)
    
    # Kết hợp tất cả đặc trưng
    if feature_dfs:
        result = pd.concat(feature_dfs, axis=1)
        return result
    else:
        # Fallback cho trường hợp không đủ dữ liệu
        return extract_features_single_window(draws[-min(len(draws), 70):], "w70")


# Tương thích ngược với API cũ
def extract_features(draws_70: List[List[int]]) -> pd.DataFrame:
    """Hàm tương thích ngược với API cũ."""
    return extract_enhanced_features(draws_70, min_draws=3)
