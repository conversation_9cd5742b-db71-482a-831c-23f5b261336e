import pymysql
import pandas as pd
import numpy as np
from tqdm import tqdm

DB_CFG = dict(
    host="127.0.0.1",
    user="root",
    password="",
    db="lstx",
    charset="utf8mb4",
    cursorclass=pymysql.cursors.DictCursor
)


def load_draws():
    conn = pymysql.connect(**DB_CFG)
    cursor = conn.cursor()
    cursor.execute("SELECT results FROM history_kennos ORDER BY period ASC")
    rows = cursor.fetchall()
    conn.close()
    draws = [list(map(int, row['results'].split(','))) for row in rows]
    return draws


def extract_features(draws, window_size=70):
    features = []
    labels = []

    for i in tqdm(range(window_size, len(draws) - 1), desc="Generating features"):
        hist = draws[i - window_size:i]
        next_draw = set(draws[i])  # kỳ tiếp theo

        miss_matrix = []  # 80 số
        for num in range(1, 81):
            tr_status = [(0 if num in draw else 1) for draw in hist]  # 1 = trượt

            rolling_miss = sum(tr_status[-3:]) / 3
            decay_miss = sum(tr_status[j] * np.exp(-0.5 * j) for j in range(len(tr_status)))
            accel = (tr_status[-1] - 2 * tr_status[-2] + tr_status[-3]) if len(tr_status) >= 3 else 0

            miss_matrix.append((rolling_miss, decay_miss, accel))

        row_feats = []
        row_labels = []
        for num in range(1, 81):
            f = miss_matrix[num - 1]
            row_feats.extend(f)

            label_value = 0 if num in next_draw else 1  # label = 1 nếu TRƯỢT
            row_labels.append(label_value)

        features.append(row_feats)
        labels.append(row_labels)

    columns = []
    for num in range(1, 81):
        columns += [f'rolling_miss_{num}', f'decay_miss_{num}', f'acceleration_{num}']
    label_cols = [f'label_{i}' for i in range(1, 81)]

    dataset = pd.DataFrame(features, columns=columns)
    for i, label_name in enumerate(label_cols):
        dataset[label_name] = [row[i] for row in labels]

    dataset.to_csv('features.csv', index=False)


def analyze_block3_pattern(draws):
    """Phân tích mẫu xuất hiện trong các block 3 kỳ."""
    num_blocks = len(draws) // 3
    patterns = {}
    
    for b in range(num_blocks):
        start_idx = b * 3
        block = draws[start_idx:start_idx + 3]
        
        for pos, draw in enumerate(block):
            for num in draw:
                if num not in patterns:
                    patterns[num] = [0, 0, 0]  # Vị trí 0, 1, 2
                patterns[num][pos] += 1
    
    return patterns

def generate_training_data(draws, window_size=70):
    """Tạo dữ liệu huấn luyện với đặc trưng nâng cao."""
    features = []
    labels = []
    
    for i in tqdm(range(window_size, len(draws) - 1), desc="Generating features"):
        hist = draws[i - window_size:i]
        next_draw = set(draws[i])  # kỳ tiếp theo
        
        # Tạo đặc trưng cơ bản
        df_feat = extract_multi_window_features(hist)
        
        # Phân tích block 3 kỳ cho 30 kỳ gần nhất
        if len(hist) >= 30:
            recent_30 = hist[-29:] + [list(next_draw)]  # 29 kỳ gần nhất + kỳ dự đoán
            block3_patterns = analyze_block3_pattern(recent_30)
            
            # Thêm đặc trưng block 3 kỳ
            for num in range(1, 81):
                if num in block3_patterns:
                    pattern = block3_patterns[num]
                    
                    # Tổng số lần xuất hiện
                    df_feat[f"block3_total_{num}"] = sum(pattern)
                    
                    # Số lần xuất hiện ở mỗi vị trí
                    for pos in range(3):
                        df_feat[f"block3_pos{pos+1}_{num}"] = pattern[pos]
                    
                    # Vị trí xuất hiện nhiều nhất
                    max_pos = np.argmax(pattern) if sum(pattern) > 0 else -1
                    df_feat[f"block3_max_pos_{num}"] = max_pos + 1 if max_pos >= 0 else 0
                    
                    # Tỷ lệ xuất hiện ở vị trí 3 (vị trí dự đoán)
                    df_feat[f"block3_pos3_ratio_{num}"] = pattern[2] / max(1, sum(pattern))
                else:
                    df_feat[f"block3_total_{num}"] = 0
                    df_feat[f"block3_pos1_{num}"] = 0
                    df_feat[f"block3_pos2_{num}"] = 0
                    df_feat[f"block3_pos3_{num}"] = 0
                    df_feat[f"block3_max_pos_{num}"] = 0
                    df_feat[f"block3_pos3_ratio_{num}"] = 0
        
        # Tạo nhãn
        label_row = []
        for num in range(1, 81):
            label_value = 0 if num in next_draw else 1  # label = 1 nếu TRƯỢT
            label_row.append(label_value)
        
        features.append(df_feat.iloc[0].to_dict())
        labels.append(label_row)
    
    # Tạo DataFrame
    df_features = pd.DataFrame(features)
    
    # Thêm nhãn
    for i in range(1, 81):
        df_features[f'label_{i}'] = [row[i-1] for row in labels]
    
    return df_features


if __name__ == '__main__':
    draws = load_draws()
    extract_features(draws)
