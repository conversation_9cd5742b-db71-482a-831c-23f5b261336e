import pymysql
import pandas as pd
import numpy as np
from tqdm import tqdm

DB_CFG = dict(
    host="127.0.0.1",
    user="root",
    password="",
    db="lstx",
    charset="utf8mb4",
    cursorclass=pymysql.cursors.DictCursor
)


def load_draws():
    conn = pymysql.connect(**DB_CFG)
    cursor = conn.cursor()
    cursor.execute("SELECT results FROM history_kennos ORDER BY period ASC")
    rows = cursor.fetchall()
    conn.close()
    draws = [list(map(int, row['results'].split(','))) for row in rows]
    return draws


def extract_features(draws, window_size=70):
    features = []
    labels = []

    for i in tqdm(range(window_size, len(draws) - 1), desc="Generating features"):
        hist = draws[i - window_size:i]
        next_draw = set(draws[i])  # kỳ tiếp theo

        miss_matrix = []  # 80 số
        for num in range(1, 81):
            tr_status = [(0 if num in draw else 1) for draw in hist]  # 1 = trượt

            rolling_miss = sum(tr_status[-3:]) / 3
            decay_miss = sum(tr_status[j] * np.exp(-0.5 * j) for j in range(len(tr_status)))
            accel = (tr_status[-1] - 2 * tr_status[-2] + tr_status[-3]) if len(tr_status) >= 3 else 0

            miss_matrix.append((rolling_miss, decay_miss, accel))

        row_feats = []
        row_labels = []
        for num in range(1, 81):
            f = miss_matrix[num - 1]
            row_feats.extend(f)

            label_value = 0 if num in next_draw else 1  # label = 1 nếu TRƯỢT
            row_labels.append(label_value)

        features.append(row_feats)
        labels.append(row_labels)

    columns = []
    for num in range(1, 81):
        columns += [f'rolling_miss_{num}', f'decay_miss_{num}', f'acceleration_{num}']
    label_cols = [f'label_{i}' for i in range(1, 81)]

    dataset = pd.DataFrame(features, columns=columns)
    for i, label_name in enumerate(label_cols):
        dataset[label_name] = [row[i] for row in labels]

    dataset.to_csv('features.csv', index=False)


if __name__ == '__main__':
    draws = load_draws()
    extract_features(draws)
