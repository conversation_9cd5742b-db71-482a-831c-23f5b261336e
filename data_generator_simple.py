import pymysql
import pandas as pd
import numpy as np
from tqdm import tqdm
import joblib
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Database configuration
DB_CFG = dict(
    host="127.0.0.1",
    user="root",
    password="",
    db="lstx",
    charset="utf8mb4",
    cursorclass=pymysql.cursors.DictCursor
)

def load_data_from_db():
    """Load all data from database."""
    print("Connecting to database...")
    conn = pymysql.connect(**DB_CFG)
    cursor = conn.cursor()
    
    print("Loading data from history_kennos...")
    cursor.execute("SELECT period, date, time, results FROM history_kennos ORDER BY period ASC")
    rows = cursor.fetchall()
    conn.close()
    
    print(f"Loaded {len(rows)} records from database")
    
    # Convert to structured data
    data_list = []
    for row in rows:
        try:
            # Parse results string to list of integers
            results = [int(x.strip()) for x in row['results'].split(',')]
            
            data_item = {
                'period': row['period'],
                'date': str(row['date']) if row['date'] else '',
                'time': str(row['time']) if row['time'] else '',
                'results': results
            }
            data_list.append(data_item)
        except Exception as e:
            print(f"Error parsing row {row['period']}: {e}")
            continue
    
    print(f"Successfully parsed {len(data_list)} records")
    return data_list

def extract_basic_features(draws, window_size=70):
    """Extract basic features for single window."""
    if len(draws) < 3:
        return None
    
    rounds = len(draws)
    features = {}
    
    # Create miss matrix (1 = miss, 0 = hit)
    miss_mat = np.ones((rounds, 80), dtype=int)
    for r, draw in enumerate(draws):
        for n in draw:
            if 1 <= n <= 80:
                miss_mat[r, n - 1] = 0
    
    # Extract features for each number
    for num in range(1, 81):
        col = miss_mat[:, num - 1]
        
        # Rolling miss rate (last 3 rounds)
        rolling_miss = col[-3:].mean()
        
        # Decay weighted miss
        decay_weights = np.exp(-0.5 * np.arange(rounds)[::-1])
        decay_miss = np.sum(col * decay_weights)
        
        # Recent frequency
        recent_freq = np.sum(1 - col[-10:]) if rounds >= 10 else np.sum(1 - col)
        
        # Features
        features[f"rolling_miss_{num}"] = rolling_miss
        features[f"decay_miss_{num}"] = decay_miss
        features[f"recent_freq_{num}"] = recent_freq
        features[f"total_miss_{num}"] = np.sum(col)
    
    return features

def group_data_by_date(data_list):
    """CHỨC NĂNG MỚI DUY NHẤT: Group data by date for daily training."""
    print("Grouping data by date...")
    
    # Group by date
    date_groups = {}
    for item in data_list:
        date = item['date']
        if date not in date_groups:
            date_groups[date] = []
        date_groups[date].append(item)
    
    print(f"Found {len(date_groups)} unique dates")
    
    # Sort dates
    sorted_dates = sorted(date_groups.keys())
    
    return date_groups, sorted_dates

def generate_training_data(data_list, window_size=70, output_file="training_features.pkl"):
    """Generate training data with daily grouping."""
    print(f"Generating training data with window size {window_size}")
    print(f"Total available records: {len(data_list)}")
    
    if len(data_list) < window_size + 1:
        raise ValueError(f"Not enough data. Need at least {window_size + 1} records")
    
    # CHỨC NĂNG MỚI: Group by date
    date_groups, sorted_dates = group_data_by_date(data_list)
    
    all_features = []
    all_labels = []
    
    # Process each date group
    for date in tqdm(sorted_dates, desc="Processing dates"):
        date_data = date_groups[date]
        
        # Skip if not enough data for this date
        if len(date_data) < 2:
            continue
            
        # Use all previous data as history for this date
        historical_data = []
        for prev_date in sorted_dates:
            if prev_date < date:
                historical_data.extend(date_groups[prev_date])
        
        # Need enough historical data
        if len(historical_data) < window_size:
            continue
            
        # Use last window_size records as features
        hist_draws = [item['results'] for item in historical_data[-window_size:]]
        
        # Predict each period in current date
        for i, target_item in enumerate(date_data):
            try:
                # Extract features from historical data
                features = extract_basic_features(hist_draws, window_size)
                if features is None:
                    continue
                
                # Generate labels (1 = miss, 0 = hit)
                target_result = set(target_item['results'])
                labels = {}
                for num in range(1, 81):
                    labels[f"label_{num}"] = 1 if num not in target_result else 0
                
                all_features.append(features)
                all_labels.append(labels)
                
                # Update historical data with current result for next prediction
                hist_draws.append(target_item['results'])
                if len(hist_draws) > window_size:
                    hist_draws.pop(0)
                    
            except Exception as e:
                print(f"Error processing {date} period {i}: {e}")
                continue
    
    print(f"Generated {len(all_features)} training samples")
    
    # Save results
    final_data = {
        'features': all_features,
        'labels': all_labels,
        'sample_count': len(all_features),
        'window_size': window_size,
        'generated_at': datetime.now().isoformat(),
        'daily_grouped': True  # Mark as daily grouped
    }
    
    print(f"Saving training data to {output_file}")
    joblib.dump(final_data, output_file)
    
    print(f"Training data generation completed!")
    print(f"Generated {len(all_features)} samples with daily grouping")
    print(f"Features per sample: {len(all_features[0]) if all_features else 0}")
    print(f"Saved to: {output_file}")
    
    return len(all_features)

def main():
    print("=== KENO TRAINING DATA GENERATOR (SIMPLE + DAILY GROUPING) ===")
    
    # Load data from database
    data_list = load_data_from_db()
    
    if len(data_list) == 0:
        print("No data loaded from database!")
        return
    
    # Generate training data with daily grouping
    sample_count = generate_training_data(
        data_list, 
        window_size=70, 
        output_file="training_features.pkl"
    )
    
    print(f"\nCompleted! Generated {sample_count} training samples")

if __name__ == "__main__":
    main()
