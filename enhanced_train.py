import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.metrics import roc_auc_score, accuracy_score
from tqdm import tqdm
import joblib
import pymysql
from enhanced_feature_engineering import extract_enhanced_features
from adaptive_learning import AdaptiveLearningSystem
from datetime import datetime, timedelta
import argparse
import os

# Database config
DB_CFG = dict(
    host="127.0.0.1",
    user="root",
    password="",
    db="lstx",
    charset="utf8mb4",
    cursorclass=pymysql.cursors.DictCursor
)


def load_draws_with_timestamps():
    """Load dữ liệu từ database với timestamp."""
    conn = pymysql.connect(**DB_CFG)
    cursor = conn.cursor()
    
    # Lấy dữ liệu với timestamp
    cursor.execute("""
        SELECT results, period, created_at 
        FROM history_kennos 
        ORDER BY period ASC
    """)
    rows = cursor.fetchall()
    conn.close()
    
    data = []
    for row in rows:
        draws = list(map(int, row['results'].split(',')))
        timestamp = row['created_at'].strftime("%Y%m%d_%H%M%S") if row['created_at'] else None
        
        data.append({
            'draws': draws,
            'period': row['period'],
            'timestamp': timestamp
        })
    
    return data


def generate_enhanced_training_data(data_list, window_size=70, min_samples=1000):
    """Tạo dữ liệu training với enhanced features."""
    features = []
    labels = []
    timestamps = []
    
    print(f"Generating training data from {len(data_list)} draws...")
    
    for i in tqdm(range(window_size, len(data_list) - 1), desc="Processing"):
        # Lấy history và next draw
        hist_data = data_list[i - window_size:i]
        next_data = data_list[i]
        
        hist_draws = [item['draws'] for item in hist_data]
        next_draw = set(next_data['draws'])
        timestamp = next_data.get('timestamp')
        
        try:
            # Trích xuất enhanced features
            df_feat = extract_enhanced_features(hist_draws, timestamp)
            
            # Tạo labels (1 = trượt, 0 = trúng)
            row_labels = []
            for num in range(1, 81):
                label_value = 1 if num not in next_draw else 0
                row_labels.append(label_value)
            
            features.append(df_feat.iloc[0].values)
            labels.append(row_labels)
            timestamps.append(timestamp)
            
        except Exception as e:
            print(f"Error processing sample {i}: {e}")
            continue
        
        # Giới hạn số lượng samples để tránh quá tải
        if len(features) >= min_samples:
            break
    
    if not features:
        raise ValueError("No valid training samples generated")
    
    # Tạo DataFrame
    feature_names = df_feat.columns.tolist()
    X = pd.DataFrame(features, columns=feature_names)
    
    # Tạo labels DataFrame
    label_cols = [f'label_{i}' for i in range(1, 81)]
    y = pd.DataFrame(labels, columns=label_cols)
    
    return X, y, timestamps


def train_enhanced_models(X, y, timestamps=None):
    """Training enhanced models với time-based validation."""
    print(f"Training enhanced models with {len(X)} samples...")
    
    # Lưu feature columns
    with open('enhanced_feature_columns.txt', 'w') as f:
        for col in X.columns:
            f.write(col + '\n')
    
    # Time-based split nếu có timestamps
    if timestamps:
        # Sắp xếp theo thời gian
        time_indices = sorted(range(len(timestamps)), 
                            key=lambda i: timestamps[i] if timestamps[i] else "")
        
        # Chia 80-20 theo thời gian
        split_idx = int(len(time_indices) * 0.8)
        train_indices = time_indices[:split_idx]
        test_indices = time_indices[split_idx:]
        
        X_train = X.iloc[train_indices]
        X_test = X.iloc[test_indices]
        y_train = y.iloc[train_indices]
        y_test = y.iloc[test_indices]
    else:
        # Random split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
    
    models = {}
    auc_scores = []
    feature_importances = {}
    
    # Enhanced LightGBM parameters
    lgb_params = {
        'objective': 'binary',
        'boosting_type': 'gbdt',
        'n_estimators': 300,
        'learning_rate': 0.05,
        'num_leaves': 31,
        'feature_fraction': 0.8,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'min_data_in_leaf': 20,
        'max_depth': 8,
        'random_state': 42,
        'verbose': -1
    }
    
    # Training cho từng số
    for label in tqdm(y.columns, desc="Training models"):
        y_tr = y_train[label]
        y_te = y_test[label]
        
        # Kiểm tra class balance
        if len(np.unique(y_tr)) < 2:
            print(f"Skipping {label} - insufficient class diversity")
            continue
        
        model = lgb.LGBMClassifier(**lgb_params)
        
        try:
            model.fit(
                X_train, y_tr,
                eval_set=[(X_test, y_te)],
                callbacks=[lgb.early_stopping(30)],
                eval_metric='auc'
            )
            
            # Đánh giá
            preds = model.predict_proba(X_test)[:, 1]
            auc = roc_auc_score(y_te, preds)
            auc_scores.append(auc)
            
            models[label] = model
            feature_importances[label] = dict(zip(X.columns, model.feature_importances_))
            
        except Exception as e:
            print(f"Error training {label}: {e}")
            continue
    
    mean_auc = np.mean(auc_scores) if auc_scores else 0
    print(f'Mean AUC across all labels: {mean_auc:.4f}')
    print(f'Successfully trained {len(models)} models')
    
    # Lưu models
    joblib.dump(models, 'enhanced_multi_label_models.pkl')
    joblib.dump(feature_importances, 'enhanced_feature_importances.pkl')
    
    # Lưu training results
    results = {
        'mean_auc': mean_auc,
        'auc_scores': dict(zip([label for label in y.columns if label in models], auc_scores)),
        'model_count': len(models),
        'feature_count': len(X.columns),
        'training_samples': len(X_train),
        'test_samples': len(X_test),
        'timestamp': datetime.now().isoformat()
    }
    joblib.dump(results, 'enhanced_training_results.pkl')
    
    return models, results


def train_adaptive_system(data_list, window_size=70):
    """Training adaptive learning system."""
    print("Training adaptive learning system...")
    
    adaptive_system = AdaptiveLearningSystem()
    
    # Group data theo time period
    from collections import defaultdict
    time_grouped_data = defaultdict(list)
    
    for i in range(window_size, len(data_list) - 1):
        hist_data = data_list[i - window_size:i]
        next_data = data_list[i]
        
        hist_draws = [item['draws'] for item in hist_data]
        actual_result = next_data['draws']
        timestamp = next_data.get('timestamp')
        
        if timestamp:
            from enhanced_feature_engineering import get_time_period
            try:
                dt = datetime.strptime(timestamp, "%Y%m%d_%H%M%S")
                time_period = get_time_period(dt.hour)
            except:
                time_period = 'general'
        else:
            time_period = 'general'
        
        training_item = {
            'draws': hist_draws,
            'actual_result': actual_result,
            'timestamp': timestamp
        }
        
        time_grouped_data[time_period].append(training_item)
    
    # Training cho từng time period
    for time_period, training_data in time_grouped_data.items():
        if len(training_data) >= 20:  # Cần ít nhất 20 samples
            adaptive_system.train_time_specific_model(time_period, training_data)
    
    # Lưu adaptive system
    adaptive_system.save_models()
    
    return adaptive_system


def main():
    parser = argparse.ArgumentParser(description='Enhanced Keno Training System')
    parser.add_argument('--mode', choices=['enhanced', 'adaptive', 'both'], 
                       default='both', help='Training mode')
    parser.add_argument('--window', type=int, default=70, 
                       help='Window size for historical data')
    parser.add_argument('--samples', type=int, default=2000, 
                       help='Maximum training samples')
    
    args = parser.parse_args()
    
    print("Loading data from database...")
    data_list = load_draws_with_timestamps()
    print(f"Loaded {len(data_list)} draws")
    
    if args.mode in ['enhanced', 'both']:
        print("\n=== Training Enhanced Models ===")
        X, y, timestamps = generate_enhanced_training_data(
            data_list, args.window, args.samples
        )
        
        models, results = train_enhanced_models(X, y, timestamps)
        print(f"Enhanced training completed. Mean AUC: {results['mean_auc']:.4f}")
    
    if args.mode in ['adaptive', 'both']:
        print("\n=== Training Adaptive System ===")
        adaptive_system = train_adaptive_system(data_list, args.window)
        print("Adaptive training completed")
    
    print("\n=== Training Summary ===")
    if args.mode in ['enhanced', 'both']:
        print(f"Enhanced models: {results['model_count']} models trained")
        print(f"Features: {results['feature_count']}")
        print(f"Training samples: {results['training_samples']}")
    
    if args.mode in ['adaptive', 'both']:
        summary = adaptive_system.get_performance_summary()
        print(f"Adaptive models: {len(summary)} time periods")
    
    print("\nTraining completed successfully!")


if __name__ == "__main__":
    main()
