import numpy as np
import pandas as pd
import joblib
import lightgbm as lgb
from typing import List, Dict, Tu<PERSON>
from datetime import datetime, timedelta
from collections import defaultdict
from enhanced_feature_engineering import extract_enhanced_features, get_time_period
import os
import warnings
warnings.filterwarnings('ignore')


class AdaptiveLearningSystem:
    """
    H<PERSON> thống học thích ứng cho dự đoán Keno:
    - Group theo ngày và thời gian
    - Tự động retrain từ kết quả thực tế
    - Adaptive weights dựa trên performance
    """
    
    def __init__(self, model_dir: str = "adaptive_models"):
        self.model_dir = model_dir
        os.makedirs(model_dir, exist_ok=True)
        
        # Lưu trữ mô hình theo time period
        self.time_models = {}
        
        # Lưu trữ performance history
        self.performance_history = defaultdict(list)
        
        # Adaptive weights cho từng time period
        self.time_weights = defaultdict(lambda: 1.0)
        
        # Lưu trữ dữ liệu training gần đây
        self.recent_training_data = defaultdict(list)
        
        # Ngưỡng để retrain
        self.retrain_threshold = 50  # <PERSON><PERSON> samples mới để trigger retrain
        
        # Load existing models nếu có
        self.load_models()
    
    def get_time_context(self, timestamp: str = None) -> Dict:
        """Lấy context thời gian."""
        if not timestamp:
            dt = datetime.now()
        else:
            try:
                dt = datetime.strptime(timestamp, "%Y%m%d_%H%M%S")
            except:
                dt = datetime.now()
        
        return {
            'hour': dt.hour,
            'day_of_week': dt.weekday(),
            'time_period': get_time_period(dt.hour),
            'is_weekend': dt.weekday() >= 5,
            'date_str': dt.strftime("%Y%m%d")
        }
    
    def prepare_training_data(self, data_list: List[Dict]) -> Tuple[pd.DataFrame, Dict]:
        """Chuẩn bị dữ liệu training."""
        X_list = []
        y_dict = defaultdict(list)
        
        for item in data_list:
            draws = item['draws']
            actual_result = item.get('actual_result', [])
            timestamp = item.get('timestamp', '')
            
            # Trích xuất đặc trưng
            try:
                features = extract_enhanced_features(draws, timestamp)
                X_list.append(features)
                
                # Tạo labels (1 = trượt, 0 = trúng)
                for num in range(1, 81):
                    y_dict[f"label_{num}"].append(1 if num not in actual_result else 0)
            except Exception as e:
                print(f"Error processing item: {e}")
                continue
        
        if not X_list:
            return pd.DataFrame(), {}
        
        # Kết hợp features
        X = pd.concat(X_list, ignore_index=True)
        
        return X, dict(y_dict)
    
    def train_time_specific_model(self, time_period: str, training_data: List[Dict]):
        """Training mô hình cho time period cụ thể."""
        if len(training_data) < 10:
            print(f"Not enough data for {time_period}: {len(training_data)} samples")
            return
        
        print(f"Training model for {time_period} with {len(training_data)} samples")
        
        X, y_dict = self.prepare_training_data(training_data)
        
        if X.empty:
            print(f"No valid features for {time_period}")
            return
        
        # Training mô hình cho từng số
        models = {}
        
        for num in range(1, 81):
            label_key = f"label_{num}"
            if label_key not in y_dict or len(y_dict[label_key]) == 0:
                continue
            
            y = np.array(y_dict[label_key])
            
            # Kiểm tra có đủ positive/negative samples
            if len(np.unique(y)) < 2:
                continue
            
            # Training LightGBM với early stopping
            model = lgb.LGBMClassifier(
                n_estimators=200,
                learning_rate=0.05,
                num_leaves=31,
                feature_fraction=0.8,
                bagging_fraction=0.8,
                bagging_freq=5,
                min_data_in_leaf=10,
                random_state=42,
                verbose=-1
            )
            
            try:
                # Split data for validation
                split_idx = int(len(X) * 0.8)
                X_train, X_val = X.iloc[:split_idx], X.iloc[split_idx:]
                y_train, y_val = y[:split_idx], y[split_idx:]
                
                model.fit(
                    X_train, y_train,
                    eval_set=[(X_val, y_val)],
                    callbacks=[lgb.early_stopping(20)],
                    eval_metric='auc'
                )
                
                models[label_key] = model
                
            except Exception as e:
                print(f"Error training model for {time_period}, {label_key}: {e}")
                continue
        
        if models:
            self.time_models[time_period] = models
            print(f"Successfully trained {len(models)} models for {time_period}")
    
    def predict_with_context(self, draws: List[List[int]], timestamp: str = None) -> Dict:
        """Dự đoán có tính đến context thời gian."""
        time_context = self.get_time_context(timestamp)
        time_period = time_context['time_period']
        
        # Trích xuất đặc trưng
        try:
            features = extract_enhanced_features(draws, timestamp)
        except Exception as e:
            print(f"Error extracting features: {e}")
            return {num: 0.5 for num in range(1, 81)}
        
        predictions = {}
        
        # Ưu tiên sử dụng mô hình cho time period hiện tại
        model_priorities = [
            time_period,
            'general'  # fallback
        ]
        
        for num in range(1, 81):
            label_key = f"label_{num}"
            pred_prob = 0.5  # default
            
            # Tìm mô hình phù hợp
            for model_key in model_priorities:
                if model_key in self.time_models and label_key in self.time_models[model_key]:
                    model = self.time_models[model_key][label_key]
                    try:
                        pred_prob = model.predict_proba(features)[:, 1][0]
                        
                        # Điều chỉnh dựa trên performance weight
                        weight = self.time_weights[time_period]
                        pred_prob = pred_prob * weight
                        
                        break
                    except Exception as e:
                        print(f"Error predicting with model {model_key}: {e}")
                        continue
            
            predictions[num] = pred_prob
        
        return predictions
    
    def update_with_result(self, draws: List[List[int]], actual_result: List[int], 
                          predictions: Dict, timestamp: str = None):
        """Cập nhật hệ thống với kết quả thực tế."""
        time_context = self.get_time_context(timestamp)
        time_period = time_context['time_period']
        
        # Tính accuracy
        correct_predictions = 0
        total_predictions = 80
        
        for num in range(1, 81):
            predicted_miss = predictions.get(num, 0.5) > 0.5
            actual_miss = num not in actual_result
            
            if predicted_miss == actual_miss:
                correct_predictions += 1
        
        accuracy = correct_predictions / total_predictions
        
        # Lưu performance
        self.performance_history[time_period].append({
            'timestamp': timestamp or datetime.now().strftime("%Y%m%d_%H%M%S"),
            'accuracy': accuracy,
            'predictions': predictions.copy(),
            'actual_result': actual_result.copy()
        })
        
        # Cập nhật adaptive weight
        self.update_adaptive_weight(time_period, accuracy)
        
        # Lưu dữ liệu để retrain
        training_item = {
            'draws': draws,
            'actual_result': actual_result,
            'timestamp': timestamp or datetime.now().strftime("%Y%m%d_%H%M%S")
        }
        
        self.recent_training_data[time_period].append(training_item)
        
        # Kiểm tra có cần retrain không
        if len(self.recent_training_data[time_period]) >= self.retrain_threshold:
            self.retrain_model(time_period)
    
    def update_adaptive_weight(self, time_period: str, accuracy: float):
        """Cập nhật adaptive weight dựa trên accuracy."""
        # Lấy accuracy trung bình của 10 lần gần nhất
        recent_accuracies = [item['accuracy'] for item in self.performance_history[time_period][-10:]]
        avg_accuracy = np.mean(recent_accuracies) if recent_accuracies else 0.5
        
        # Điều chỉnh weight
        if avg_accuracy > 0.6:  # Performance tốt
            self.time_weights[time_period] = min(1.2, self.time_weights[time_period] * 1.01)
        elif avg_accuracy < 0.4:  # Performance kém
            self.time_weights[time_period] = max(0.8, self.time_weights[time_period] * 0.99)
        
        print(f"Updated weight for {time_period}: {self.time_weights[time_period]:.3f} (accuracy: {avg_accuracy:.3f})")
    
    def retrain_model(self, time_period: str):
        """Retrain mô hình cho time period cụ thể."""
        print(f"Retraining model for {time_period}...")
        
        # Lấy dữ liệu training
        training_data = self.recent_training_data[time_period]
        
        # Training lại
        self.train_time_specific_model(time_period, training_data)
        
        # Clear dữ liệu đã sử dụng (giữ lại 20% để tránh mất dữ liệu)
        keep_count = max(10, len(training_data) // 5)
        self.recent_training_data[time_period] = training_data[-keep_count:]
        
        # Lưu mô hình
        self.save_models()
    
    def save_models(self):
        """Lưu tất cả mô hình và metadata."""
        data = {
            'time_models': self.time_models,
            'performance_history': dict(self.performance_history),
            'time_weights': dict(self.time_weights),
            'recent_training_data': dict(self.recent_training_data)
        }
        
        filepath = os.path.join(self.model_dir, 'adaptive_models.pkl')
        joblib.dump(data, filepath)
        print(f"Models saved to {filepath}")
    
    def load_models(self):
        """Load mô hình và metadata."""
        filepath = os.path.join(self.model_dir, 'adaptive_models.pkl')
        
        if os.path.exists(filepath):
            try:
                data = joblib.load(filepath)
                self.time_models = data.get('time_models', {})
                self.performance_history = defaultdict(list, data.get('performance_history', {}))
                self.time_weights = defaultdict(lambda: 1.0, data.get('time_weights', {}))
                self.recent_training_data = defaultdict(list, data.get('recent_training_data', {}))
                print(f"Models loaded from {filepath}")
                return True
            except Exception as e:
                print(f"Error loading models: {e}")
                return False
        else:
            print("No existing models found")
            return False
    
    def get_performance_summary(self) -> Dict:
        """Lấy tóm tắt performance."""
        summary = {}
        
        for time_period, history in self.performance_history.items():
            if history:
                accuracies = [item['accuracy'] for item in history]
                summary[time_period] = {
                    'count': len(history),
                    'avg_accuracy': np.mean(accuracies),
                    'recent_accuracy': np.mean(accuracies[-10:]) if len(accuracies) >= 10 else np.mean(accuracies),
                    'weight': self.time_weights[time_period]
                }
        
        return summary
