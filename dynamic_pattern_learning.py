import numpy as np
import pandas as pd
import joblib
import lightgbm as lgb
from typing import List, Dict, Tuple
from collections import defaultdict, deque
from enhanced_feature_engineering import extract_enhanced_features
import os
import warnings
warnings.filterwarnings('ignore')


class DynamicPatternLearning:
    """
    <PERSON><PERSON> thống học pattern động thay thế time-based training:
    1. Pattern Sequence Learning - Học chuỗi pattern xuất hiện
    2. Hot/Cold Cycle Detection - Phát hiện chu kỳ nóng/lạnh
    3. Frequency Momentum - Momentum tần suất xuất hiện
    4. Adaptive Threshold Learning - Học ngưỡng thích ứng
    5. Performance-Based Model Selection - Chọn model dựa trên hiệu suất
    """
    
    def __init__(self, model_dir: str = "dynamic_models"):
        self.model_dir = model_dir
        os.makedirs(model_dir, exist_ok=True)
        
        # Lưu trữ patterns
        self.sequence_patterns = defaultdict(list)  # Chuỗi pattern
        self.hot_cold_cycles = defaultdict(deque)   # Chu kỳ nóng/lạnh
        self.frequency_momentum = defaultdict(list) # Momentum tần suất
        
        # Model ensemble với performance tracking
        self.model_ensemble = {}
        self.model_performance = defaultdict(list)
        self.model_weights = defaultdict(lambda: 1.0)
        
        # Adaptive thresholds
        self.adaptive_thresholds = defaultdict(lambda: 0.5)
        
        # Recent results cho learning
        self.recent_results = deque(maxlen=100)
        self.pattern_memory = deque(maxlen=50)
        
        # Load existing data
        self.load_patterns()
    
    def extract_sequence_pattern(self, draws: List[List[int]], window: int = 5) -> Dict:
        """Trích xuất pattern chuỗi xuất hiện."""
        patterns = {}
        
        for num in range(1, 81):
            # Tạo chuỗi 0/1 cho số này
            sequence = []
            for draw in draws[-window:]:
                sequence.append(1 if num in draw else 0)
            
            # Phân tích pattern
            pattern_key = ''.join(map(str, sequence))
            
            # Tính momentum
            if len(sequence) >= 3:
                recent_trend = sum(sequence[-3:]) / 3
                prev_trend = sum(sequence[-6:-3]) / 3 if len(sequence) >= 6 else recent_trend
                momentum = recent_trend - prev_trend
            else:
                momentum = 0
            
            # Tính cycle position (vị trí trong chu kỳ)
            appearances = [i for i, x in enumerate(sequence) if x == 1]
            if len(appearances) >= 2:
                gaps = np.diff(appearances)
                avg_gap = np.mean(gaps)
                last_gap = len(sequence) - 1 - appearances[-1] if appearances else len(sequence)
                cycle_position = last_gap / max(1, avg_gap)
            else:
                cycle_position = 0
            
            patterns[num] = {
                'sequence': pattern_key,
                'momentum': momentum,
                'cycle_position': cycle_position,
                'recent_frequency': sum(sequence) / len(sequence),
                'trend_strength': abs(momentum)
            }
        
        return patterns
    
    def detect_hot_cold_cycles(self, draws: List[List[int]], cycle_length: int = 10) -> Dict:
        """Phát hiện chu kỳ nóng/lạnh của từng số."""
        cycles = {}
        
        for num in range(1, 81):
            # Chia thành các cycle
            appearances_per_cycle = []
            
            for i in range(0, len(draws), cycle_length):
                cycle_draws = draws[i:i + cycle_length]
                appearances = sum(1 for draw in cycle_draws if num in draw)
                appearances_per_cycle.append(appearances)
            
            if len(appearances_per_cycle) >= 3:
                # Phân tích xu hướng cycle
                recent_cycles = appearances_per_cycle[-3:]
                avg_recent = np.mean(recent_cycles)
                
                # So sánh với overall average
                overall_avg = np.mean(appearances_per_cycle)
                
                # Xác định trạng thái
                if avg_recent > overall_avg * 1.2:
                    status = 'hot'
                    intensity = (avg_recent - overall_avg) / overall_avg
                elif avg_recent < overall_avg * 0.8:
                    status = 'cold'
                    intensity = (overall_avg - avg_recent) / overall_avg
                else:
                    status = 'neutral'
                    intensity = 0
                
                # Dự đoán cycle tiếp theo
                if len(appearances_per_cycle) >= 5:
                    trend = np.polyfit(range(len(recent_cycles)), recent_cycles, 1)[0]
                    next_cycle_prediction = recent_cycles[-1] + trend
                else:
                    next_cycle_prediction = avg_recent
                
                cycles[num] = {
                    'status': status,
                    'intensity': intensity,
                    'recent_avg': avg_recent,
                    'overall_avg': overall_avg,
                    'next_prediction': max(0, next_cycle_prediction),
                    'trend': trend if len(appearances_per_cycle) >= 5 else 0
                }
            else:
                cycles[num] = {
                    'status': 'neutral',
                    'intensity': 0,
                    'recent_avg': 0,
                    'overall_avg': 0,
                    'next_prediction': 0,
                    'trend': 0
                }
        
        return cycles
    
    def calculate_frequency_momentum(self, draws: List[List[int]]) -> Dict:
        """Tính momentum tần suất xuất hiện."""
        momentum = {}
        
        for num in range(1, 81):
            # Tần suất trong các window khác nhau
            freq_5 = sum(1 for draw in draws[-5:] if num in draw) / 5
            freq_10 = sum(1 for draw in draws[-10:] if num in draw) / 10 if len(draws) >= 10 else freq_5
            freq_20 = sum(1 for draw in draws[-20:] if num in draw) / 20 if len(draws) >= 20 else freq_10
            
            # Tính momentum (gia tốc tần suất)
            short_momentum = freq_5 - freq_10
            long_momentum = freq_10 - freq_20
            
            # Tính acceleration
            acceleration = short_momentum - long_momentum
            
            # Dự đoán xu hướng
            if acceleration > 0.1:
                trend_prediction = 'increasing'
                strength = acceleration
            elif acceleration < -0.1:
                trend_prediction = 'decreasing'
                strength = abs(acceleration)
            else:
                trend_prediction = 'stable'
                strength = 0
            
            momentum[num] = {
                'short_momentum': short_momentum,
                'long_momentum': long_momentum,
                'acceleration': acceleration,
                'trend_prediction': trend_prediction,
                'strength': strength,
                'current_frequency': freq_5
            }
        
        return momentum
    
    def learn_adaptive_thresholds(self, predictions: Dict, actual_result: List[int]):
        """Học ngưỡng thích ứng cho từng số."""
        for num in range(1, 81):
            pred_prob = predictions.get(num, 0.5)
            actual_miss = num not in actual_result
            
            current_threshold = self.adaptive_thresholds[num]
            
            # Điều chỉnh threshold dựa trên kết quả
            if actual_miss and pred_prob < current_threshold:
                # Số thực sự trượt nhưng dự đoán không trượt -> giảm threshold
                self.adaptive_thresholds[num] = max(0.1, current_threshold - 0.01)
            elif not actual_miss and pred_prob > current_threshold:
                # Số thực sự không trượt nhưng dự đoán trượt -> tăng threshold
                self.adaptive_thresholds[num] = min(0.9, current_threshold + 0.01)
    
    def train_ensemble_models(self, training_data: List[Dict]):
        """Training ensemble models với các strategy khác nhau."""
        if len(training_data) < 50:
            print(f"Not enough training data: {len(training_data)}")
            return
        
        print(f"Training ensemble models with {len(training_data)} samples")
        
        # Chuẩn bị dữ liệu
        X_list = []
        y_dict = defaultdict(list)
        
        for item in training_data:
            draws = item['draws']
            actual_result = item.get('actual_result', [])
            
            try:
                # Extract base features
                base_features = extract_enhanced_features(draws)
                
                # Extract dynamic patterns
                seq_patterns = self.extract_sequence_pattern(draws)
                hot_cold = self.detect_hot_cold_cycles(draws)
                freq_momentum = self.calculate_frequency_momentum(draws)
                
                # Combine features
                combined_features = base_features.copy()
                
                for num in range(1, 81):
                    # Sequence pattern features
                    combined_features[f"seq_momentum_{num}"] = seq_patterns[num]['momentum']
                    combined_features[f"cycle_position_{num}"] = seq_patterns[num]['cycle_position']
                    combined_features[f"trend_strength_{num}"] = seq_patterns[num]['trend_strength']
                    
                    # Hot/cold cycle features
                    combined_features[f"cycle_intensity_{num}"] = hot_cold[num]['intensity']
                    combined_features[f"cycle_prediction_{num}"] = hot_cold[num]['next_prediction']
                    combined_features[f"cycle_trend_{num}"] = hot_cold[num]['trend']
                    
                    # Frequency momentum features
                    combined_features[f"freq_acceleration_{num}"] = freq_momentum[num]['acceleration']
                    combined_features[f"freq_strength_{num}"] = freq_momentum[num]['strength']
                
                X_list.append(combined_features)
                
                # Labels
                for num in range(1, 81):
                    y_dict[f"label_{num}"].append(1 if num not in actual_result else 0)
                
            except Exception as e:
                print(f"Error processing training item: {e}")
                continue
        
        if not X_list:
            print("No valid training samples")
            return
        
        # Combine features
        X = pd.concat(X_list, ignore_index=True)
        
        # Train multiple models với strategies khác nhau
        strategies = {
            'conservative': {
                'n_estimators': 200,
                'learning_rate': 0.03,
                'num_leaves': 15,
                'min_data_in_leaf': 30
            },
            'aggressive': {
                'n_estimators': 300,
                'learning_rate': 0.08,
                'num_leaves': 63,
                'min_data_in_leaf': 10
            },
            'balanced': {
                'n_estimators': 250,
                'learning_rate': 0.05,
                'num_leaves': 31,
                'min_data_in_leaf': 20
            }
        }
        
        for strategy_name, params in strategies.items():
            print(f"Training {strategy_name} strategy...")
            
            models = {}
            for num in range(1, 81):
                label_key = f"label_{num}"
                if label_key not in y_dict:
                    continue
                
                y = np.array(y_dict[label_key])
                if len(np.unique(y)) < 2:
                    continue
                
                model = lgb.LGBMClassifier(
                    objective='binary',
                    boosting_type='gbdt',
                    random_state=42,
                    verbose=-1,
                    **params
                )
                
                try:
                    # Split for validation
                    split_idx = int(len(X) * 0.8)
                    X_train, X_val = X.iloc[:split_idx], X.iloc[split_idx:]
                    y_train, y_val = y[:split_idx], y[split_idx:]
                    
                    model.fit(
                        X_train, y_train,
                        eval_set=[(X_val, y_val)],
                        callbacks=[lgb.early_stopping(20)],
                        eval_metric='auc'
                    )
                    
                    models[label_key] = model
                    
                except Exception as e:
                    print(f"Error training {strategy_name} model for {label_key}: {e}")
                    continue
            
            if models:
                self.model_ensemble[strategy_name] = models
                print(f"Trained {len(models)} models for {strategy_name}")
    
    def predict_with_ensemble(self, draws: List[List[int]]) -> Dict:
        """Dự đoán với ensemble models."""
        try:
            # Extract features
            base_features = extract_enhanced_features(draws)
            
            # Extract dynamic patterns
            seq_patterns = self.extract_sequence_pattern(draws)
            hot_cold = self.detect_hot_cold_cycles(draws)
            freq_momentum = self.calculate_frequency_momentum(draws)
            
            # Combine features
            combined_features = base_features.copy()
            
            for num in range(1, 81):
                # Add dynamic features
                combined_features[f"seq_momentum_{num}"] = seq_patterns[num]['momentum']
                combined_features[f"cycle_position_{num}"] = seq_patterns[num]['cycle_position']
                combined_features[f"trend_strength_{num}"] = seq_patterns[num]['trend_strength']
                combined_features[f"cycle_intensity_{num}"] = hot_cold[num]['intensity']
                combined_features[f"cycle_prediction_{num}"] = hot_cold[num]['next_prediction']
                combined_features[f"cycle_trend_{num}"] = hot_cold[num]['trend']
                combined_features[f"freq_acceleration_{num}"] = freq_momentum[num]['acceleration']
                combined_features[f"freq_strength_{num}"] = freq_momentum[num]['strength']
            
            # Predict with ensemble
            ensemble_predictions = {}
            
            for num in range(1, 81):
                label_key = f"label_{num}"
                predictions = []
                weights = []
                
                # Get predictions from all strategies
                for strategy_name, models in self.model_ensemble.items():
                    if label_key in models:
                        try:
                            pred = models[label_key].predict_proba(combined_features)[:, 1][0]
                            predictions.append(pred)
                            weights.append(self.model_weights[f"{strategy_name}_{num}"])
                        except:
                            continue
                
                if predictions:
                    # Weighted average
                    if sum(weights) > 0:
                        final_pred = sum(p * w for p, w in zip(predictions, weights)) / sum(weights)
                    else:
                        final_pred = np.mean(predictions)
                    
                    # Apply dynamic adjustments
                    # Hot/cold cycle adjustment
                    if hot_cold[num]['status'] == 'hot' and hot_cold[num]['intensity'] > 0.3:
                        final_pred *= 0.8  # Giảm xác suất trượt cho số hot
                    elif hot_cold[num]['status'] == 'cold' and hot_cold[num]['intensity'] > 0.3:
                        final_pred *= 1.2  # Tăng xác suất trượt cho số cold
                    
                    # Frequency momentum adjustment
                    if freq_momentum[num]['trend_prediction'] == 'increasing':
                        final_pred *= (1 - freq_momentum[num]['strength'])
                    elif freq_momentum[num]['trend_prediction'] == 'decreasing':
                        final_pred *= (1 + freq_momentum[num]['strength'])
                    
                    ensemble_predictions[num] = np.clip(final_pred, 0.01, 0.99)
                else:
                    ensemble_predictions[num] = 0.5
            
            return ensemble_predictions
            
        except Exception as e:
            print(f"Error in ensemble prediction: {e}")
            return {num: 0.5 for num in range(1, 81)}
    
    def update_with_result(self, draws: List[List[int]], actual_result: List[int], predictions: Dict):
        """Cập nhật hệ thống với kết quả thực tế."""
        # Lưu kết quả
        self.recent_results.append({
            'draws': draws,
            'actual_result': actual_result,
            'predictions': predictions.copy()
        })
        
        # Update adaptive thresholds
        self.learn_adaptive_thresholds(predictions, actual_result)
        
        # Update model weights dựa trên performance
        self.update_model_weights(predictions, actual_result)
        
        # Lưu patterns
        self.save_patterns()
    
    def update_model_weights(self, predictions: Dict, actual_result: List[int]):
        """Cập nhật weights của models dựa trên performance."""
        for strategy_name in self.model_ensemble.keys():
            for num in range(1, 81):
                weight_key = f"{strategy_name}_{num}"
                
                predicted_miss = predictions.get(num, 0.5) > self.adaptive_thresholds[num]
                actual_miss = num not in actual_result
                
                if predicted_miss == actual_miss:
                    # Dự đoán đúng
                    self.model_weights[weight_key] = min(2.0, self.model_weights[weight_key] * 1.01)
                else:
                    # Dự đoán sai
                    self.model_weights[weight_key] = max(0.5, self.model_weights[weight_key] * 0.99)
    
    def save_patterns(self):
        """Lưu patterns và models."""
        data = {
            'model_ensemble': self.model_ensemble,
            'model_weights': dict(self.model_weights),
            'adaptive_thresholds': dict(self.adaptive_thresholds),
            'recent_results': list(self.recent_results)
        }
        
        filepath = os.path.join(self.model_dir, 'dynamic_patterns.pkl')
        joblib.dump(data, filepath)
    
    def load_patterns(self):
        """Load patterns và models."""
        filepath = os.path.join(self.model_dir, 'dynamic_patterns.pkl')
        
        if os.path.exists(filepath):
            try:
                data = joblib.load(filepath)
                self.model_ensemble = data.get('model_ensemble', {})
                self.model_weights = defaultdict(lambda: 1.0, data.get('model_weights', {}))
                self.adaptive_thresholds = defaultdict(lambda: 0.5, data.get('adaptive_thresholds', {}))
                self.recent_results = deque(data.get('recent_results', []), maxlen=100)
                print("Dynamic patterns loaded successfully")
                return True
            except Exception as e:
                print(f"Error loading patterns: {e}")
                return False
        return False
