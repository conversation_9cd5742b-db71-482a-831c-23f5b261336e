import numpy as np
import pandas as pd
from typing import List, Dict, Tuple
from scipy import stats
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')


def extract_features(draws: List[List[int]]) -> pd.DataFrame:
    """Tạo DataFrame với đặc trưng tần suất và mật độ phân bố.

    * rolling_miss_i   = tỉ lệ trượt của số i trong 3 kỳ cuối.
    * decay_miss_i     = Σ trượt * e^(−0.5·k) (k tính từ kỳ gần nhất).
    * acceleration_i   = đạo hàm bậc 2 trên chuỗi miss‑rate 3‑kỳ.
    * block3_freq_i    = tần suất xuất hiện của số i trong các block 3 kỳ.
    * block3_pos_i     = vị trí xuất hiện phổ biến của số i trong block 3 kỳ.

    Args:
        draws: <PERSON><PERSON> sách các kỳ quay, mỗi kỳ là danh sách các số từ 1-80

    Returns:
        DataFrame chứa các đặc trưng được trích xuất
    """
    if len(draws) < 3:
        raise ValueError(f"draws must contain ≥ 3 rounds, received {len(draws)} rounds")

    all_nums = range(1, 81)
    rounds = len(draws)

    # ma trận 0/1: 1 = trượt, 0 = trúng
    miss_mat = np.ones((rounds, 80), dtype=int)
    for r, draw in enumerate(draws):
        for n in draw:
            if 1 <= n <= 80:
                miss_mat[r, n - 1] = 0

    # Phân tích block 3 kỳ
    num_blocks = rounds // 3
    block3_appearances = {num: [0, 0, 0] for num in all_nums}  # Vị trí 0, 1, 2 trong block
    block3_counts = {num: 0 for num in all_nums}  # Tổng số lần xuất hiện trong các block

    for b in range(num_blocks):
        block_start = rounds - (b + 1) * 3
        block_end = rounds - b * 3

        if block_start < 0:
            continue

        block_draws = draws[block_start:block_end]
        
        for pos, draw in enumerate(block_draws):
            for num in draw:
                if 1 <= num <= 80:
                    block3_appearances[num][pos] += 1
                    block3_counts[num] += 1

    feat_rows = {}
    for idx, num in enumerate(all_nums):
        col = miss_mat[:, idx]           # vector 0/1 độ dài rounds

        # --- rolling miss 3 kỳ cuối ---
        rolling_miss = col[-3:].mean()

        # --- decay weighted miss (kỳ mới quan trọng hơn) ---
        decay_weights = np.exp(-0.5 * np.arange(rounds)[::-1])
        decay_miss = np.sum(col * decay_weights)

        # --- miss acceleration (đạo hàm bậc 2) ---
        mr0 = col[-3:].mean()
        mr1 = col[-4:-1].mean() if rounds >= 4 else mr0
        mr2 = col[-5:-2].mean() if rounds >= 5 else mr1
        acceleration = mr0 - 2 * mr1 + mr2

        # --- Đặc trưng block 3 kỳ ---
        # Tần suất xuất hiện trong các block 3 kỳ
        block3_freq = block3_counts[num] / max(1, num_blocks)
        
        # Vị trí xuất hiện phổ biến trong block 3 kỳ (0, 1, 2)
        # Chuyển đổi vị trí từ (0,1,2) sang (3,2,1) theo yêu cầu
        pos_counts = block3_appearances[num]
        block3_pos = np.argmax(pos_counts) if sum(pos_counts) > 0 else -1
        block3_pos = 3 - block3_pos if block3_pos >= 0 else 0  # Chuyển 0->3, 1->2, 2->1
        
        # Tỷ lệ xuất hiện ở vị trí 3 (vị trí dự đoán)
        pos3_ratio = pos_counts[0] / max(1, sum(pos_counts))  # vị trí 0 -> 3
        
        # Số lần xuất hiện ở vị trí 3
        pos3_count = pos_counts[0]  # vị trí 0 -> 3

        # Đặc trưng cơ bản
        feat_rows[f"rolling_miss_{num}"] = rolling_miss
        feat_rows[f"decay_miss_{num}"] = decay_miss
        feat_rows[f"acceleration_{num}"] = acceleration
        feat_rows[f"block3_freq_{num}"] = block3_freq
        feat_rows[f"block3_pos_{num}"] = block3_pos
        feat_rows[f"pos3_ratio_{num}"] = pos3_ratio
        feat_rows[f"pos3_count_{num}"] = pos3_count

        # Đặc trưng nâng cao
        # 1. Tần suất xuất hiện trong các cửa sổ khác nhau
        feat_rows[f"freq_last_5_{num}"] = 1 - col[-5:].mean() if rounds >= 5 else rolling_miss
        feat_rows[f"freq_last_10_{num}"] = 1 - col[-10:].mean() if rounds >= 10 else rolling_miss
        feat_rows[f"freq_last_20_{num}"] = 1 - col[-20:].mean() if rounds >= 20 else rolling_miss

        # 2. Độ biến thiên (volatility)
        if rounds >= 10:
            recent_10 = col[-10:]
            feat_rows[f"volatility_{num}"] = np.std(recent_10)
            feat_rows[f"trend_{num}"] = np.corrcoef(np.arange(10), recent_10)[0, 1] if np.std(recent_10) > 0 else 0
        else:
            feat_rows[f"volatility_{num}"] = 0
            feat_rows[f"trend_{num}"] = 0

        # 3. Chu kỳ xuất hiện
        appearances = np.where(col == 0)[0]  # Vị trí xuất hiện
        if len(appearances) >= 2:
            gaps = np.diff(appearances)
            feat_rows[f"avg_gap_{num}"] = np.mean(gaps)
            feat_rows[f"gap_std_{num}"] = np.std(gaps)
            feat_rows[f"last_gap_{num}"] = rounds - appearances[-1] - 1
        else:
            feat_rows[f"avg_gap_{num}"] = rounds
            feat_rows[f"gap_std_{num}"] = 0
            feat_rows[f"last_gap_{num}"] = rounds

        # 4. Momentum indicators
        if rounds >= 6:
            recent_3 = col[-3:].mean()
            prev_3 = col[-6:-3].mean()
            feat_rows[f"momentum_{num}"] = recent_3 - prev_3
        else:
            feat_rows[f"momentum_{num}"] = 0

    return pd.DataFrame([feat_rows])


def extract_ensemble_features(draws: List[List[int]]) -> pd.DataFrame:
    """Trích xuất đặc trưng ensemble từ tương quan giữa các số."""
    if len(draws) < 10:
        return pd.DataFrame()

    # Ma trận xuất hiện
    appearance_matrix = np.zeros((len(draws), 80))
    for i, draw in enumerate(draws):
        for num in draw:
            if 1 <= num <= 80:
                appearance_matrix[i, num-1] = 1

    ensemble_features = {}

    # Tương quan giữa các số
    for num in range(1, 81):
        correlations = []
        for other_num in range(1, 81):
            if num != other_num:
                corr = np.corrcoef(appearance_matrix[:, num-1], appearance_matrix[:, other_num-1])[0, 1]
                if not np.isnan(corr):
                    correlations.append(abs(corr))

        if correlations:
            ensemble_features[f"max_correlation_{num}"] = max(correlations)
            ensemble_features[f"avg_correlation_{num}"] = np.mean(correlations)
            ensemble_features[f"correlation_std_{num}"] = np.std(correlations)
        else:
            ensemble_features[f"max_correlation_{num}"] = 0
            ensemble_features[f"avg_correlation_{num}"] = 0
            ensemble_features[f"correlation_std_{num}"] = 0

    # Đặc trưng nhóm số (hot/cold numbers)
    recent_10 = draws[-10:] if len(draws) >= 10 else draws
    hot_numbers = set()
    for draw in recent_10:
        hot_numbers.update(draw)

    for num in range(1, 81):
        ensemble_features[f"is_hot_{num}"] = 1 if num in hot_numbers else 0

        # Tần suất xuất hiện trong nhóm hot
        hot_freq = sum(1 for draw in recent_10 if num in draw) / len(recent_10)
        ensemble_features[f"hot_frequency_{num}"] = hot_freq

    return pd.DataFrame([ensemble_features])


def extract_statistical_features(draws: List[List[int]]) -> pd.DataFrame:
    """Trích xuất đặc trưng thống kê nâng cao."""
    if len(draws) < 5:
        return pd.DataFrame()

    stat_features = {}

    # Phân tích phân phối số trong mỗi kỳ
    draw_sizes = [len(draw) for draw in draws]
    stat_features["avg_draw_size"] = np.mean(draw_sizes)
    stat_features["draw_size_std"] = np.std(draw_sizes)

    # Phân tích khoảng cách giữa các số trong mỗi kỳ
    for i, draw in enumerate(draws[-10:]):  # Chỉ phân tích 10 kỳ gần nhất
        if len(draw) >= 2:
            sorted_draw = sorted(draw)
            gaps = np.diff(sorted_draw)
            stat_features[f"draw_{i}_avg_gap"] = np.mean(gaps)
            stat_features[f"draw_{i}_max_gap"] = max(gaps)
            stat_features[f"draw_{i}_min_gap"] = min(gaps)

    # Entropy của phân phối
    all_numbers = []
    for draw in draws[-20:]:  # 20 kỳ gần nhất
        all_numbers.extend(draw)

    if all_numbers:
        from collections import Counter
        counts = Counter(all_numbers)
        total = len(all_numbers)
        entropy = -sum((count/total) * np.log2(count/total) for count in counts.values())
        stat_features["entropy"] = entropy
    else:
        stat_features["entropy"] = 0

    return pd.DataFrame([stat_features])


def extract_multi_window_features(draws, min_draws=30):
    """Trích xuất đặc trưng từ nhiều cửa sổ thời gian khác nhau với các đặc trưng nâng cao."""
    if len(draws) < min_draws:
        raise ValueError(f"Cần ít nhất {min_draws} kỳ quay số để trích xuất đặc trưng")

    # Tạo đặc trưng cơ bản cho từng cửa sổ thời gian
    windows = [3, 7, 12, 20, 30]
    if len(draws) >= 50:
        windows.append(50)
    if len(draws) >= 70:
        windows.append(70)

    feature_dfs = []

    for window in windows:
        if len(draws) >= window:
            window_draws = draws[-window:]
            feat = extract_features(window_draws)

            # Đổi tên cột
            feat = feat.add_suffix(f"_w{window}")
            feature_dfs.append(feat)

    # Đặc trưng ensemble
    ensemble_feat = extract_ensemble_features(draws)
    if not ensemble_feat.empty:
        feature_dfs.append(ensemble_feat)

    # Đặc trưng thống kê
    stat_feat = extract_statistical_features(draws)
    if not stat_feat.empty:
        feature_dfs.append(stat_feat)

    # Kết hợp tất cả đặc trưng
    if feature_dfs:
        result = pd.concat(feature_dfs, axis=1)

        # Thêm đặc trưng tương tác
        result = add_interaction_features(result)

        return result
    else:
        return pd.DataFrame()


def add_interaction_features(df: pd.DataFrame) -> pd.DataFrame:
    """Thêm đặc trưng tương tác giữa các cửa sổ thời gian."""
    if df.empty:
        return df

    # Tìm các cột rolling_miss cho các cửa sổ khác nhau
    rolling_miss_cols = [col for col in df.columns if 'rolling_miss_' in col and '_w' in col]

    # Tạo đặc trưng tương tác cho từng số
    for num in range(1, 81):
        num_cols = [col for col in rolling_miss_cols if f'rolling_miss_{num}_w' in col]

        if len(num_cols) >= 2:
            # Tính trung bình trọng số (cửa sổ nhỏ hơn có trọng số cao hơn)
            weights = [1/int(col.split('_w')[1]) for col in num_cols]
            weighted_avg = sum(df[col].iloc[0] * w for col, w in zip(num_cols, weights)) / sum(weights)
            df[f"weighted_miss_{num}"] = weighted_avg

            # Tính độ lệch chuẩn giữa các cửa sổ
            values = [df[col].iloc[0] for col in num_cols]
            df[f"miss_volatility_{num}"] = np.std(values)

    return df
