# Hệ Thống Dự Đoán Keno Nâng Cấp

## Tổng <PERSON>uan

Hệ thống dự đoán 20 số trượt Keno đã được nâng cấp toàn diện với các tính năng:

### ✅ Các <PERSON>ải Tiến Đã Thực Hiện

1. **Multi-Window Analysis** - Phân tích đa cửa sổ thời gian
   - Window 3: <PERSON> hướng cực ngắn hạn
   - Window 12: Xu hướng ngắn hạn  
   - Window 30: Xu hướng trung hạn
   - Window 70: Xu hướng dài hạn

2. **Block 3 Pattern Analysis** - <PERSON>ân tích mẫu block 3 kỳ
   - Lấy 29 kỳ gần nhất + kỳ dự đoán = 30 kỳ
   - <PERSON><PERSON> thành 10 block 3 kỳ
   - Phân tích vị trí xuất hiện (1, 2, 3)
   - Ưu tiên số xuất hiện ≥3 lần ở vị trí 3

3. **Time-Based Training** - Training theo thời gian
   - Group theo ngày và giờ
   - Phân biệt xu hướng đầu ngà<PERSON> vs cu<PERSON><PERSON> ngày
   - <PERSON>ô hình riêng cho từng time period

4. **Adaptive Learning** - Học thích ứng
   - Tự động retrain từ kết quả thực tế
   - Adaptive weights dựa trên performance
   - Continuous learning system

## Cấu Trúc Files

```
├── enhanced_feature_engineering.py  # Feature engineering nâng cấp
├── adaptive_learning.py            # Hệ thống học thích ứng
├── enhanced_train.py               # Script training nâng cấp
├── predict_api.py                  # API nâng cấp
├── feature_engineering.py          # File gốc (tương thích ngược)
├── train_gbm.py                    # Training gốc (tương thích ngược)
└── adaptive_models/                # Thư mục lưu adaptive models
```

## Cài Đặt và Chạy

### 1. Cài Đặt Dependencies

```bash
pip install pandas numpy lightgbm scikit-learn flask pymysql tqdm joblib
```

### 2. Training Hệ Thống

#### Training Enhanced Models
```bash
python enhanced_train.py --mode enhanced --samples 2000
```

#### Training Adaptive System
```bash
python enhanced_train.py --mode adaptive --window 70
```

#### Training Cả Hai
```bash
python enhanced_train.py --mode both --samples 2000 --window 70
```

### 3. Chạy API

```bash
python predict_api.py
```

API sẽ chạy tại: `http://localhost:5049`

## API Endpoints

### 1. Dự Đoán - `/predict-10-number` (POST)

**Request:**
```json
{
  "draws": [[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20], ...],
  "timestamp": "20241222_143000"  // Optional
}
```

**Response:**
```json
{
  "top20_miss_numbers": [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20],
  "prediction_id": "20241222_143000",
  "prediction_source": "adaptive",
  "timestamp": "20241222_143000",
  "total_draws": 70
}
```

### 2. Cập Nhật Kết Quả - `/update-result` (POST)

**Request:**
```json
{
  "prediction_id": "20241222_143000",
  "actual_result": [5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,1,2,3,4],
  "draws": [[...], [...], ...]
}
```

**Response:**
```json
{
  "message": "Result updated successfully",
  "accuracy": 0.75,
  "correct_predictions": 60,
  "total_predictions": 80,
  "prediction_id": "20241222_143000"
}
```

### 3. Performance Summary - `/performance-summary` (GET)

**Response:**
```json
{
  "performance_summary": {
    "early_morning": {
      "count": 150,
      "avg_accuracy": 0.72,
      "recent_accuracy": 0.75,
      "weight": 1.05
    },
    "afternoon": {
      "count": 200,
      "avg_accuracy": 0.68,
      "recent_accuracy": 0.70,
      "weight": 0.98
    }
  },
  "total_time_periods": 5
}
```

### 4. Manual Retrain - `/retrain` (POST)

**Request:**
```json
{
  "time_period": "early_morning"
}
```

## Tính Năng Nâng Cấp Chi Tiết

### 1. Multi-Window Features

- **Window 3**: Bắt xu hướng tức thời
- **Window 12**: Xu hướng trong phiên
- **Window 30**: Xu hướng ngày
- **Window 70**: Xu hướng tuần

Mỗi window tính:
- Rolling miss rate
- Decay weighted miss
- Acceleration
- Frequency
- Volatility
- Last gap

### 2. Block 3 Pattern Analysis

```
29 kỳ gần nhất + 1 kỳ dự đoán = 30 kỳ
Chia thành 10 block 3 kỳ:

Block 1: [kỳ1, kỳ2, kỳ3]
Block 2: [kỳ4, kỳ5, kỳ6]
...
Block 10: [kỳ28, kỳ29, kỳ_dự_đoán]

Vị trí trong block: 1, 2, 3 (từ trái sang phải)
Kỳ dự đoán ở vị trí 3
```

**Logic quan trọng:**
- Nếu số X xuất hiện ≥3 lần ở vị trí 3 → Giảm 30% xác suất trượt
- Phân tích dominant position
- Tính entropy phân phối vị trí

### 3. Time-Based Learning

**Time Periods:**
- `early_morning`: 6-10h
- `late_morning`: 10-14h  
- `afternoon`: 14-18h
- `evening`: 18-22h
- `night`: 22-6h

**Adaptive Weights:**
- Performance tốt (>60%): Tăng weight 1%
- Performance kém (<40%): Giảm weight 1%
- Weight range: 0.8 - 1.2

### 4. Continuous Learning

- Auto retrain sau 50 samples mới
- Lưu trữ performance history
- Model versioning
- Fallback mechanism

## Monitoring và Debugging

### 1. Kiểm Tra Performance

```bash
curl http://localhost:5049/performance-summary
```

### 2. Trigger Manual Retrain

```bash
curl -X POST http://localhost:5049/retrain \
  -H "Content-Type: application/json" \
  -d '{"time_period": "afternoon"}'
```

### 3. Log Files

- API logs: Console output
- Model training: `enhanced_training_results.pkl`
- Performance: Trong adaptive system

## Tương Thích Ngược

Hệ thống mới hoàn toàn tương thích với API cũ:
- Tham số `draws_70` vẫn hoạt động
- Fallback về mô hình cũ nếu adaptive system lỗi
- Cấu trúc response giữ nguyên, chỉ thêm fields mới

## Kết Quả Mong Đợi

### Cải Thiện Accuracy
- Baseline: ~50-60%
- Multi-window: +5-10%
- Block 3 pattern: +3-7%
- Time-based: +2-5%
- Adaptive learning: +5-15% (theo thời gian)

### Tổng Cải Thiện Dự Kiến: 15-37%

## Troubleshooting

### 1. Lỗi Import
```bash
# Đảm bảo tất cả files trong cùng thư mục
# Kiểm tra Python path
```

### 2. Lỗi Database
```bash
# Kiểm tra config database trong enhanced_train.py
# Đảm bảo table history_kennos tồn tại
```

### 3. Lỗi Memory
```bash
# Giảm --samples trong training
# Tăng RAM hoặc sử dụng batch processing
```

### 4. Performance Thấp
```bash
# Kiểm tra data quality
# Tăng số lượng training samples
# Điều chỉnh hyperparameters
```
