import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import roc_auc_score
from tqdm import tqdm
import joblib

# Load data
df = pd.read_csv('features.csv')

# Features and labels
X = df.drop(columns=[f'label_{i}' for i in range(1, 81)])
y = df[[f'label_{i}' for i in range(1, 81)]]

# Save feature column order for later use
with open('feature_columns.txt', 'w') as f:
    for col in X.columns:
        f.write(col + '\n')

# Split data
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

models = {}
auc_scores = []

# Multi-label training using independent binary classifiers
for label in tqdm(y.columns, desc="Training models"):
    y_tr = y_train[label]
    y_te = y_test[label]

    model = lgb.LGBMClassifier(
        objective='binary',
        boosting_type='gbdt',
        n_estimators=200,
        learning_rate=0.05,
        num_leaves=31,
        random_state=42, 
    )

    model.fit(X_train, y_tr, eval_set=[(X_test, y_te)], callbacks=[lgb.early_stopping(20)])

    preds = model.predict_proba(X_test)[:, 1]
    auc = roc_auc_score(y_te, preds)
    auc_scores.append(auc)

    models[label] = model

mean_auc = np.mean(auc_scores)
print(f'Mean AUC across all labels: {mean_auc:.4f}')

# Save models
joblib.dump(models, 'multi_label_models.pkl')