import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import roc_auc_score
from tqdm import tqdm
import joblib
import os
from datetime import datetime

# C<PERSON>u hình huấn luyện
CONFIG = {
    'n_estimators': 300,
    'learning_rate': 0.05,
    'num_leaves': 31,
    'feature_fraction': 0.8,
    'bagging_fraction': 0.8,
    'bagging_freq': 5,
    'min_data_in_leaf': 50,
    'max_depth': 8,
    'random_state': 42,
}

def train_initial_models(data_path='features.csv'):
    """Huấn luyện mô hình ban đầu."""
    print("Loading data...")
    df = pd.read_csv(data_path)
    
    # Tách đặc trưng và nhãn
    X = df.drop(columns=[f'label_{i}' for i in range(1, 81)])
    y = df[[f'label_{i}' for i in range(1, 81)]]
    
    # <PERSON><PERSON><PERSON> thứ tự cột đặc trưng
    with open('feature_columns.txt', 'w') as f:
        for col in X.columns:
            f.write(col + '\n')
    
    # <PERSON>a d<PERSON> liệu
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    models = {}
    auc_scores = []
    feature_importances = {}
    
    # Huấn luyện mô hình cho từng số
    for label in tqdm(y.columns, desc="Training models"):
        y_tr = y_train[label]
        y_te = y_test[label]
        
        model = lgb.LGBMClassifier(**CONFIG)
        model.fit(
            X_train, y_tr, 
            eval_set=[(X_test, y_te)], 
            callbacks=[lgb.early_stopping(20)],
            eval_metric='auc'
        )
        
        # Đánh giá mô hình
        preds = model.predict_proba(X_test)[:, 1]
        auc = roc_auc_score(y_te, preds)
        auc_scores.append(auc)
        
        # Lưu mô hình
        models[label] = model
        
        # Lưu độ quan trọng của đặc trưng
        feature_importances[label] = dict(zip(X.columns, model.feature_importances_))
    
    mean_auc = np.mean(auc_scores)
    print(f'Mean AUC across all labels: {mean_auc:.4f}')
    
    # Lưu mô hình và thông tin
    joblib.dump(models, 'multi_label_models.pkl')
    joblib.dump(feature_importances, 'feature_importances.pkl')
    
    # Lưu cấu hình và kết quả
    results = {
        'mean_auc': mean_auc,
        'auc_scores': dict(zip(y.columns, auc_scores)),
        'config': CONFIG,
        'timestamp': datetime.now().isoformat()
    }
    joblib.dump(results, 'training_results.pkl')
    
    return models, results

def update_models(new_data_path, existing_models_path='multi_label_models.pkl'):
    """Cập nhật mô hình với dữ liệu mới."""
    print(f"Updating models with new data: {new_data_path}")
    
    # Đọc dữ liệu mới
    new_df = pd.read_csv(new_data_path)
    
    # Tách đặc trưng và nhãn
    X_new = new_df.drop(columns=[f'label_{i}' for i in range(1, 81)])
    y_new = new_df[[f'label_{i}' for i in range(1, 81)]]
    
    # Đọc mô hình hiện tại
    models = joblib.load(existing_models_path)
    
    # Cập nhật mô hình
    updated_models = {}
    auc_scores = []
    
    for label in tqdm(y_new.columns, desc="Updating models"):
        # Lấy mô hình hiện tại
        model = models[label]
        
        # Chia dữ liệu mới
        X_train, X_test, y_train, y_test = train_test_split(X_new, y_new[label], test_size=0.2)
        
        # Cập nhật mô hình
        train_data = lgb.Dataset(X_train, label=y_train)
        valid_data = lgb.Dataset(X_test, label=y_test, reference=train_data)
        
        # Chuyển đổi từ LGBMClassifier sang Booster để cập nhật
        booster = lgb.train(
            params=model.get_params(),
            train_set=train_data,
            valid_sets=[valid_data],
            num_boost_round=50,
            init_model=model.booster_,
            callbacks=[lgb.early_stopping(10)]
        )
        
        # Chuyển đổi lại thành LGBMClassifier
        updated_model = lgb.LGBMClassifier(**CONFIG)
        updated_model._Booster = booster
        updated_model._n_features = X_new.shape[1]
        updated_model._n_classes = 2
        
        # Đánh giá mô hình
        preds = updated_model.predict_proba(X_test)[:, 1]
        auc = roc_auc_score(y_test, preds)
        auc_scores.append(auc)
        
        updated_models[label] = updated_model
    
    mean_auc = np.mean(auc_scores)
    print(f'Mean AUC after update: {mean_auc:.4f}')
    
    # Lưu mô hình cập nhật
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    joblib.dump(updated_models, f'multi_label_models_updated_{timestamp}.pkl')
    
    # Thay thế mô hình cũ
    os.replace(f'multi_label_models_updated_{timestamp}.pkl', 'multi_label_models.pkl')
    
    return updated_models, mean_auc

def continuous_learning_loop(data_dir='continuous_data'):
    """Vòng lặp học liên tục từ dữ liệu mới."""
    # Đảm bảo thư mục tồn tại
    os.makedirs(data_dir, exist_ok=True)
    
    # Kiểm tra xem đã có mô hình ban đầu chưa
    if not os.path.exists('multi_label_models.pkl'):
        print("No initial model found. Training from scratch...")
        train_initial_models()
    
    # Vòng lặp liên tục
    while True:
        # Tìm tất cả file dữ liệu mới
        new_files = [f for f in os.listdir(data_dir) if f.endswith('.csv') and not f.startswith('processed_')]
        
        if not new_files:
            print("No new data files found. Waiting...")
            import time
            time.sleep(3600)  # Đợi 1 giờ
            continue
        
        # Xử lý từng file dữ liệu mới
        for file in new_files:
            file_path = os.path.join(data_dir, file)
            print(f"Processing new data file: {file_path}")
            
            # Cập nhật mô hình
            update_models(file_path)
            
            # Đánh dấu file đã xử lý
            os.rename(file_path, os.path.join(data_dir, f"processed_{file}"))
            
            print(f"Completed processing: {file}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Train or update Keno prediction models')
    parser.add_argument('--mode', choices=['train', 'update', 'continuous'], default='train',
                        help='Operation mode: train (initial), update (with new data), or continuous (learning loop)')
    parser.add_argument('--data', type=str, default='features.csv',
                        help='Path to the data file')
    
    args = parser.parse_args()
    
    if args.mode == 'train':
        train_initial_models(args.data)
    elif args.mode == 'update':
        update_models(args.data)
    elif args.mode == 'continuous':
        continuous_learning_loop()
