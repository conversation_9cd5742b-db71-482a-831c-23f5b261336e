{{-- resources/views/bet_manager.blade.php --}}
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Manager Monitor</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <style>
        body {
            background-color: #121212;
            color: #e0e0e0;
        }
        .table-dark, .table-dark th, .table-dark td {
            background-color: #1e1e1e !important;
            color: #e0e0e0;
            border-color: #333;
        }
        .form-control, .form-select {
            background-color: #1e1e1e;
            color: #e0e0e0;
            border: 1px solid #444;
        }
        .form-control:focus, .form-select:focus {
            border-color: #888;
            box-shadow: none;
        }
        .btn {
            background-color: #2a2a2a;
            color: #e0e0e0;
            border: 1px solid #444;
        }
        .btn:hover {
            background-color: #444;
        }
        .card.bg-secondary {
            background-color: #2a2a2a !important;
        }
        .badge.bg-info {
            background-color: #2a9fd6;
            color: #121212;
        }
        th.sortable:hover {
            cursor: pointer;
            background-color: #2a2a2a !important;
        }
        .preview-section {
            background-color: #2a2a2a;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            display: none;
        }
        .status-hit { color: #28a745; }
        .status-miss { color: #dc3545; }
        .status-unchanged { color: #6c757d; }
    </style>
</head>

<body class="antialiased">
<div class="container-fluid bg-dark text-light py-4 min-vh-100">

    <h2 class="text-info mb-4">Manager Monitor</h2>

    {{-- Select Cache Key và Filter --}}
    <form method="GET" action="{{ route('bet.manager') }}" class="mb-4">
        <div class="row">
            <div class="col-md-4">
                <label class="form-label">Chọn Cache Key:</label>
                <select name="key" class="form-control bg-dark text-light" onchange="this.form.submit()">
                    <option value="">-- Chọn key --</option>
                    @foreach ($availableKeys as $key)
                        <option value="{{ $key }}" {{ $selectedKey === $key ? 'selected' : '' }}>
                            {{ $key }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Filter CountMiss:</label>
                <select name="filter_count_miss" class="form-control bg-dark text-light" onchange="this.form.submit()">
                    @for($i = 13; $i <= 23; $i++)
                        <option value="{{ $i }}" {{ $filterCountMiss == $i ? 'selected' : '' }}>
                            >= {{ $i }}
                        </option>
                    @endfor
                </select>
            </div>
        </div>
    </form>

    @if ($selectedKey && count($dataBet) > 0)
        {{-- Tổng quan countMiss --}}
        <div class="card bg-secondary text-light mb-4">
            <div class="card-header">Tổng quan CountMiss</div>
            <div class="card-body">
                @foreach ($summary as $count => $total)
                    <span class="badge bg-info me-2">CountMiss {{ $count }} total: {{ $total }}</span>
                @endforeach
            </div>
        </div>

        {{-- Preview Section --}}
        <div class="preview-section" id="previewSection">
            <h5 class="text-warning">Preview Kết Quả</h5>
            <div class="row mb-3">
                <div class="col-md-6">
                    <label class="form-label">Nhập kết quả kỳ hiện tại (format: 01|02|03|...):</label>
                    <input type="text" id="resultInput" class="form-control bg-dark text-light" 
                           placeholder="VD: 01|05|12|15|20|25|30|35|40|45|50|55|60|65|70|75|78|79|80">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="button" class="btn btn-info" onclick="calculatePreview()">Tính Preview</button>
                </div>
            </div>
            <div id="previewResults"></div>
        </div>

        {{-- Form chính (POST) --}}
        <form id="mainForm" method="POST" action="{{ route('bet.update') }}">
            @csrf
            <input type="hidden" name="key" value="{{ $selectedKey }}">

            {{-- Ẩn để JS set kill/add single --}}
            <input type="hidden" name="kill_value" value="0">
            <input type="hidden" name="add_value" value="0">

            {{-- Apply to All Checkbox --}}
            <div class="row mb-3">
                <div class="col-12">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="apply_to_all" value="1" id="applyToAll" checked>
                        <label class="form-check-label text-warning" for="applyToAll">
                            <strong>Áp dụng cho TẤT CẢ cache keys</strong>
                        </label>
                    </div>
                </div>
            </div>

            {{-- Bulk kill/add --}}
            <div class="row mb-3">
                <div class="col-md-2">
                    <label>Giảm CountMiss cho selected:</label>
                    <input type="number" name="bulk_kill_value" class="form-control bg-dark text-light" value="3" min="1">
                </div>
                <div class="col-md-2">
                    <label>Tăng CountMiss cho selected:</label>
                    <input type="number" name="bulk_add_value" class="form-control bg-dark text-light" value="1" min="1">
                </div>
                <div class="col-md-3">
                    <label>Kill pair (ex: 05,07,15):</label>
                    <input type="text" name="kill_pairs" class="form-control bg-dark text-light" placeholder="Nhập các số cách nhau bởi dấu phẩy">
                    <input type="number" name="kill_pair_value" class="form-control bg-dark text-light mt-2" value="3" min="1" placeholder="Giảm bao nhiêu">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-warning w-100">Áp dụng Bulk</button>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="button" class="btn btn-info w-100" onclick="togglePreview()">Toggle Preview</button>
                </div>
            </div>

            {{-- Data Table --}}
            <div class="table-responsive">
                <table id="dataTable" class="table table-dark table-striped align-middle text-center">
                    <thead>
                    <tr>
                        <th class="sortable" onclick="sortTable(0)">STT ▲▼</th>
                        <th class="sortable" onclick="sortTable(1)">Index ▲▼</th>
                        <th class="sortable" onclick="sortTable(2)">Ip ▲▼</th>
                        <th class="sortable" onclick="sortTable(3)">CountMiss ▲▼</th>
                        <th>Actions</th>
                        <th><input type="checkbox" onclick="toggleAll(this)"> All</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach ($dataBet as $index => $item)
                        <tr>
                            <td>{{ $loop->iteration }}</td>
                            <td>{{ $index }}</td>
                            <td>{{ implode(',', $item['numberBet']) }}</td>
                            <td class="text-warning">{{ $item['countMiss'] }}</td>
                            <td>
                                <div class="d-flex justify-content-center align-items-center gap-1">
                                    {{-- Kill Single --}}
                                    <input type="number"
                                           id="kill_{{ $index }}"
                                           value="3"
                                           min="1"
                                           class="form-control form-control-sm bg-dark text-light"
                                           style="width: 60px;">
                                    <button type="button"
                                            class="btn btn-sm btn-danger"
                                            onclick="killSingle({{ $index }})">
                                        Kill
                                    </button>
                                    {{-- Add Single --}}
                                    <input type="number"
                                           id="add_{{ $index }}"
                                           value="1"
                                           min="1"
                                           class="form-control form-control-sm bg-dark text-light"
                                           style="width: 60px;">
                                    <button type="button"
                                            class="btn btn-sm btn-success"
                                            onclick="addSingle({{ $index }})">
                                        Add
                                    </button>
                                </div>
                            </td>
                            <td>
                                <input type="checkbox" name="selected[{{ $index }}]" value="1">
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </form>
    @elseif($selectedKey)
        <div class="alert alert-warning mt-4">Không có dữ liệu trong cache này.</div>
    @endif

</div>

<script>
    // Toggle chọn hết
    function toggleAll(source) {
        document.querySelectorAll('input[type="checkbox"][name^="selected"]').forEach(cb => cb.checked = source.checked);
    }

    // Kill Single
    function killSingle(index) {
        const killInput = document.getElementById(`kill_${index}`);
        const hiddenKill = document.querySelector('input[name="kill_value"]');
        const hiddenAdd  = document.querySelector('input[name="add_value"]');
        const bulkKill   = document.querySelector('input[name="bulk_kill_value"]');
        const bulkAdd    = document.querySelector('input[name="bulk_add_value"]');
        const checkbox   = document.querySelector(`input[name="selected[${index}]"]`);

        if (killInput && hiddenKill && hiddenAdd && bulkKill && bulkAdd && checkbox) {
            hiddenKill.value = killInput.value || 3;
            hiddenAdd.value  = 0;
            bulkKill.value   = 0;
            bulkAdd.value    = 0;
            checkbox.checked = true;
            document.getElementById('mainForm').submit();
        }
    }

    // Add Single
    function addSingle(index) {
        const addInput   = document.getElementById(`add_${index}`);
        const hiddenAdd  = document.querySelector('input[name="add_value"]');
        const hiddenKill = document.querySelector('input[name="kill_value"]');
        const bulkKill   = document.querySelector('input[name="bulk_kill_value"]');
        const bulkAdd    = document.querySelector('input[name="bulk_add_value"]');
        const checkbox   = document.querySelector(`input[name="selected[${index}]"]`);

        if (addInput && hiddenAdd && hiddenKill && bulkKill && bulkAdd && checkbox) {
            hiddenAdd.value  = addInput.value || 1;
            hiddenKill.value = 0;
            bulkKill.value   = 0;
            bulkAdd.value    = 0;
            checkbox.checked = true;
            document.getElementById('mainForm').submit();
        }
    }

    // Sort table
    function sortTable(colIndex) {
        const table = document.getElementById("dataTable");
        const tbody = table.tBodies[0];
        const rows = Array.from(tbody.rows);
        const isNumeric = !isNaN(rows[0].cells[colIndex].innerText.trim());

        let asc = table.getAttribute("data-sort-asc-" + colIndex) !== "true";
        table.setAttribute("data-sort-asc-" + colIndex, asc ? "true" : "false");

        rows.sort((a, b) => {
            let aText = a.cells[colIndex].innerText.trim();
            let bText = b.cells[colIndex].innerText.trim();
            if (isNumeric) {
                return asc
                    ? parseInt(aText) - parseInt(bText)
                    : parseInt(bText) - parseInt(aText);
            } else {
                return asc
                    ? aText.localeCompare(bText)
                    : bText.localeCompare(aText);
            }
        });
        rows.forEach(row => tbody.appendChild(row));
    }

    // Toggle Preview Section
    function togglePreview() {
        const previewSection = document.getElementById('previewSection');
        previewSection.style.display = previewSection.style.display === 'none' ? 'block' : 'none';
    }

    // Calculate Preview
    function calculatePreview() {
        const resultInput = document.getElementById('resultInput').value;
        const selectedKey = '{{ $selectedKey }}';
        
        if (!resultInput.trim()) {
            alert('Vui lòng nhập kết quả kỳ hiện tại');
            return;
        }

        fetch('{{ route("bet.preview") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                key: selectedKey,
                result_input: resultInput
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayPreviewResults(data.previewData);
            } else {
                alert('Lỗi: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi tính preview');
        });
    }

    // Display Preview Results
    function displayPreviewResults(previewData) {
        const resultsDiv = document.getElementById('previewResults');
        
        let html = '<div class="table-responsive"><table class="table table-dark table-sm">';
        html += '<thead><tr><th>Index</th><th>Numbers</th><th>Old CountMiss</th><th>New CountMiss</th><th>Change</th><th>Status</th></tr></thead><tbody>';
        
        previewData.forEach(item => {
            let statusClass = '';
            let statusText = '';
            
            switch(item.status) {
                case 'hit_reset':
                    statusClass = 'status-hit';
                    statusText = 'HIT - Reset to 0';
                    break;
                case 'miss_increased':
                    statusClass = 'status-miss';
                    statusText = 'MISS - Increased';
                    break;
                default:
                    statusClass = 'status-unchanged';
                    statusText = 'Unchanged';
            }
            
            html += `<tr>
                <td>${item.index}</td>
                <td>${item.numberBet.join(',')}</td>
                <td>${item.oldCountMiss}</td>
                <td>${item.newCountMiss}</td>
                <td>${item.change > 0 ? '+' : ''}${item.change}</td>
                <td class="${statusClass}">${statusText}</td>
            </tr>`;
        });
        
        html += '</tbody></table></div>';
        resultsDiv.innerHTML = html;
    }
</script>
</body>
</html>
