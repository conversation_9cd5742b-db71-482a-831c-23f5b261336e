// mwor project vaf chyaj casc lenh:
Thứ tự chạy sau khi cài đặt xong: gen node

0. khởi động git bash 
source venv/Scripts/activate
python predict_api.py

Run:
python predict_api.py



setup:
source venv/Scripts/activate
# Bước 1: Generate training data từ database
python data_generator.py

# Bước 2: Train ensemble models
python model_trainer.py

# Bước 3: Chạy API prediction
python predict_api.py

RUN: 

source venv/Scripts/activate
python predict_v9.py

II. PYTHON: 
0. KHỞI ĐỘNG GIT CHUẨN MÔI TRƯỜNG: 
      source venv/Scripts/activate

1. TRAIN MODEL: 
      PYTHONPATH=. python app/train_model.py

2. KHỞI CHẠY: 
      python -m api.app

3. xoas cache git bassh
rm -rf __pycache__ app/__pycache__ api/__pycache__



==========================================================

✅ 1. KHỞI TẠO PROJECT – LẦN ĐẦU
Bước 1: Tạo virtual environment (khuyên dùng)
python -m venv venv
source venv/Scripts/activate    # (Windows)

Bước 2: Cài đặt thư viện

pip install -r requirements.txt

✅ 2. HUẤN LUYỆN DATA
Bước 1: Kiểm tra file training

Bước 2: Chạy script huấn luyện mô hình
📦 Sau khi chạy xong:

Sẽ tạo ra file mô hình: models/model.keras

✅ 3. CHẠY API LOCAL http://localhost:5000/predict
Bước 1: Chạy Flask API
python api/app.py
Bạn sẽ thấy console in ra:

csharp
* Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
Mặc định Flask chạy ở localhost:5000

✅ 4. GỌI THỬ API TEST
Bạn có thể test bằng curl, Postman hoặc Laravel HTTP client.

Ví dụ gọi bằng curl

curl -X POST http://localhost:5000/predict \
  -H "Content-Type: application/json" \
  -d '{"history": [[1,2,3,...20 số], [...], ...]}' 
Laravel gọi:
$response = Http::post('http://localhost:5000/predict', [
    'history' => $history_array // mảng các kỳ, mỗi kỳ gồm 20 số
]);

$result = $response->json()['predict'];

cấu trúc tất cả cùng cấp hết:

feature_columns.txt
feature_engineering.py
features.csv
generate_csv.py
multi_label_models.pkl
predict_api.py
requirements.txt
train_gbm.py