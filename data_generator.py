import pymysql
import pandas as pd
import numpy as np
from tqdm import tqdm
import joblib
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Database configuration
DB_CFG = dict(
    host="127.0.0.1",
    user="root",
    password="",
    db="lstx",
    charset="utf8mb4",
    cursorclass=pymysql.cursors.DictCursor
)


def load_data_from_db():
    """Load all data from database."""
    print("Connecting to database...")
    conn = pymysql.connect(**DB_CFG)
    cursor = conn.cursor()
    
    print("Loading data from history_kennos...")
    cursor.execute("SELECT period, date, time, results FROM history_kennos ORDER BY period ASC")
    rows = cursor.fetchall()
    conn.close()
    
    print(f"Loaded {len(rows)} records from database")
    
    # Convert to structured data
    data_list = []
    for row in rows:
        try:
            # Parse results string to list of integers
            results = [int(x.strip()) for x in row['results'].split(',')]
            
            data_item = {
                'period': row['period'],
                'date': str(row['date']) if row['date'] else '',
                'time': str(row['time']) if row['time'] else '',
                'results': results
            }
            data_list.append(data_item)
        except Exception as e:
            print(f"Error parsing row {row['period']}: {e}")
            continue
    
    print(f"Successfully parsed {len(data_list)} records")
    return data_list


def extract_basic_features(draws, window_size=70):
    """Extract basic features for single window."""
    if len(draws) < 3:
        return None
    
    rounds = len(draws)
    features = {}
    
    # Create miss matrix (1 = miss, 0 = hit)
    miss_mat = np.ones((rounds, 80), dtype=int)
    for r, draw in enumerate(draws):
        for n in draw:
            if 1 <= n <= 80:
                miss_mat[r, n - 1] = 0
    
    # Extract features for each number
    for num in range(1, 81):
        col = miss_mat[:, num - 1]
        
        # Rolling miss rate (last 3 rounds)
        rolling_miss = col[-3:].mean()
        
        # Decay weighted miss
        decay_weights = np.exp(-0.5 * np.arange(rounds)[::-1])
        decay_miss = np.sum(col * decay_weights)
        
        # Acceleration (second derivative)
        mr0 = col[-3:].mean()
        mr1 = col[-4:-1].mean() if rounds >= 4 else mr0
        mr2 = col[-5:-2].mean() if rounds >= 5 else mr1
        acceleration = mr0 - 2 * mr1 + mr2
        
        # Frequency
        frequency = 1 - col.mean()
        
        # Volatility
        volatility = np.std(col) if rounds > 1 else 0
        
        # Last gap
        last_gap = rounds
        for i in range(rounds-1, -1, -1):
            if col[i] == 0:
                last_gap = rounds - 1 - i
                break
        
        features[f"rolling_miss_{num}"] = rolling_miss
        features[f"decay_miss_{num}"] = decay_miss
        features[f"acceleration_{num}"] = acceleration
        features[f"frequency_{num}"] = frequency
        features[f"volatility_{num}"] = volatility
        features[f"last_gap_{num}"] = last_gap
    
    return features


def analyze_block3_pattern(draws):
    """Analyze block 3 pattern: 29 recent + 1 prediction = 30 draws = 10 blocks."""
    if len(draws) < 29:
        return {}
    
    # Take 29 recent draws + placeholder for prediction
    recent_29 = draws[-29:]
    all_30_draws = recent_29 + [[]]  # Empty list as placeholder
    
    patterns = {}
    
    # Analyze 10 blocks of 3 draws each
    for block_idx in range(10):
        start_idx = block_idx * 3
        block = all_30_draws[start_idx:start_idx + 3]
        
        for pos, draw in enumerate(block):
            position = pos + 1  # Position 1, 2, 3
            
            for num in draw:
                if 1 <= num <= 80:
                    if num not in patterns:
                        patterns[num] = {
                            'positions': [0, 0, 0],  # Count at position 1, 2, 3
                            'total_count': 0
                        }
                    
                    patterns[num]['positions'][position - 1] += 1
                    patterns[num]['total_count'] += 1
    
    # Calculate metrics for each number
    result = {}
    for num in range(1, 81):
        if num not in patterns:
            patterns[num] = {'positions': [0, 0, 0], 'total_count': 0}
        
        pos_counts = patterns[num]['positions']
        total = patterns[num]['total_count']
        
        # Position 3 ratio (prediction position)
        pos3_ratio = pos_counts[2] / max(1, total)
        pos3_count = pos_counts[2]
        
        # Dominant position
        dominant_position = np.argmax(pos_counts) + 1 if total > 0 else 0
        
        # Block frequency
        block_frequency = total / 10.0
        
        # High position 3 probability (if appears >= 3 times at position 3)
        high_pos3_probability = 1 if pos3_count >= 3 else 0
        
        result[num] = {
            'pos3_ratio': pos3_ratio,
            'pos3_count': pos3_count,
            'dominant_position': dominant_position,
            'block_frequency': block_frequency,
            'high_pos3_probability': high_pos3_probability
        }
    
    return result


def generate_multi_window_features(draws):
    """Generate features from multiple time windows."""
    features = {}
    
    # Different time windows
    windows = [3, 12, 30, 70]
    
    for window in windows:
        if len(draws) >= window:
            window_draws = draws[-window:]
            window_features = extract_basic_features(window_draws, window)
            
            if window_features:
                # Add window suffix to feature names
                for key, value in window_features.items():
                    features[f"{key}_w{window}"] = value
    
    return features


def generate_training_data_stream(data_list, window_size=70, output_file="training_features.pkl"):
    """Generate training data with ZERO RAM accumulation - pure streaming."""
    print(f"Generating training data with window size {window_size}")
    print(f"Total available records: {len(data_list)}")

    if len(data_list) < window_size + 1:
        raise ValueError(f"Not enough data. Need at least {window_size + 1} records")

    # Calculate total samples
    total_samples = len(data_list) - window_size
    print(f"Will generate {total_samples} training samples")

    # Initialize final data structure
    final_data = {
        'features': [],
        'labels': [],
        'sample_count': 0,
        'window_size': window_size,
        'generated_at': datetime.now().isoformat()
    }

    # Process and save in small chunks - NEVER accumulate
    chunk_size = 100  # Very small chunks
    save_frequency = 1000  # Save every 1000 samples

    processed_count = 0

    for i in tqdm(range(total_samples), desc="Generating samples"):
        # Get historical data and next result
        hist_data = data_list[i:i + window_size]
        next_data = data_list[i + window_size]

        # Extract draws only
        hist_draws = [item['results'] for item in hist_data]
        next_result = set(next_data['results'])

        try:
            # Generate multi-window features
            features = generate_multi_window_features(hist_draws)

            # Add block 3 pattern features
            if len(hist_draws) >= 29:
                block3_patterns = analyze_block3_pattern(hist_draws)
                for num in range(1, 81):
                    pattern = block3_patterns[num]
                    features[f"block3_pos3_ratio_{num}"] = pattern['pos3_ratio']
                    features[f"block3_pos3_count_{num}"] = pattern['pos3_count']
                    features[f"block3_dominant_pos_{num}"] = pattern['dominant_position']
                    features[f"block3_frequency_{num}"] = pattern['block_frequency']
                    features[f"block3_high_pos3_{num}"] = pattern['high_pos3_probability']

            # Generate labels (1 = miss, 0 = hit)
            labels = {}
            for num in range(1, 81):
                labels[f"label_{num}"] = 1 if num not in next_result else 0

            # Add to current chunk
            final_data['features'].append(features)
            final_data['labels'].append(labels)
            final_data['sample_count'] += 1
            processed_count += 1

            # Save and clear every save_frequency samples
            if processed_count % save_frequency == 0:
                print(f"Saving checkpoint at {processed_count} samples...")
                joblib.dump(final_data, f"checkpoint_{output_file}")

                # Clear features and labels but keep metadata
                final_data['features'] = []
                final_data['labels'] = []

        except Exception as e:
            print(f"Error processing sample {i}: {e}")
            continue

    # Save any remaining data
    if final_data['features']:
        print(f"Saving final checkpoint...")
        joblib.dump(final_data, f"final_{output_file}")

    # Now combine all checkpoint files
    print("Combining checkpoint files...")

    checkpoint_files = []
    for i in range(0, processed_count, save_frequency):
        checkpoint_file = f"checkpoint_{output_file}"
        if os.path.exists(checkpoint_file):
            checkpoint_files.append(checkpoint_file)

    # Add final file if exists
    final_checkpoint = f"final_{output_file}"
    if os.path.exists(final_checkpoint):
        checkpoint_files.append(final_checkpoint)

    # Combine checkpoints one by one
    combined_data = {
        'features': [],
        'labels': [],
        'sample_count': processed_count,
        'window_size': window_size,
        'generated_at': datetime.now().isoformat()
    }

    for checkpoint_file in tqdm(checkpoint_files, desc="Combining checkpoints"):
        checkpoint_data = joblib.load(checkpoint_file)
        combined_data['features'].extend(checkpoint_data['features'])
        combined_data['labels'].extend(checkpoint_data['labels'])

        # Clean up checkpoint file immediately
        os.remove(checkpoint_file)
        del checkpoint_data

    # Save final result
    print(f"Saving final training data to {output_file}")
    joblib.dump(combined_data, output_file)

    print(f"Training data generation completed!")
    print(f"Generated {processed_count} samples")
    print(f"Features per sample: {len(combined_data['features'][0]) if combined_data['features'] else 0}")
    print(f"Saved to: {output_file}")

    return processed_count


def main():
    print("=== KENO TRAINING DATA GENERATOR ===")

    # Load data from database
    data_list = load_data_from_db()

    if len(data_list) < 100:
        print("ERROR: Not enough data in database")
        return

    # Generate training data with streaming approach
    sample_count = generate_training_data_stream(
        data_list,
        window_size=70,
        output_file="training_features.pkl"
    )

    print(f"Data generation completed successfully! Generated {sample_count} samples")


if __name__ == "__main__":
    main()
