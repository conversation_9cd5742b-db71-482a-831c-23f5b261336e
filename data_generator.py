import pymysql
import pandas as pd
import numpy as np
from tqdm import tqdm
import joblib
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Database configuration
DB_CFG = dict(
    host="127.0.0.1",
    user="root",
    password="",
    db="lstx",
    charset="utf8mb4",
    cursorclass=pymysql.cursors.DictCursor
)


def load_data_from_db():
    """Load all data from database."""
    print("Connecting to database...")
    conn = pymysql.connect(**DB_CFG)
    cursor = conn.cursor()
    
    print("Loading data from history_kennos...")
    cursor.execute("SELECT period, date, time, results FROM history_kennos ORDER BY period ASC")
    rows = cursor.fetchall()
    conn.close()
    
    print(f"Loaded {len(rows)} records from database")
    
    # Convert to structured data
    data_list = []
    for row in rows:
        try:
            # Parse results string to list of integers
            results = [int(x.strip()) for x in row['results'].split(',')]
            
            data_item = {
                'period': row['period'],
                'date': str(row['date']) if row['date'] else '',
                'time': str(row['time']) if row['time'] else '',
                'results': results
            }
            data_list.append(data_item)
        except Exception as e:
            print(f"Error parsing row {row['period']}: {e}")
            continue
    
    print(f"Successfully parsed {len(data_list)} records")
    return data_list


def extract_basic_features(draws, window_size=70):
    """Extract basic features for single window."""
    if len(draws) < 3:
        return None
    
    rounds = len(draws)
    features = {}
    
    # Create miss matrix (1 = miss, 0 = hit)
    miss_mat = np.ones((rounds, 80), dtype=int)
    for r, draw in enumerate(draws):
        for n in draw:
            if 1 <= n <= 80:
                miss_mat[r, n - 1] = 0
    
    # Extract features for each number
    for num in range(1, 81):
        col = miss_mat[:, num - 1]
        
        # Rolling miss rate (last 3 rounds)
        rolling_miss = col[-3:].mean()
        
        # Decay weighted miss
        decay_weights = np.exp(-0.5 * np.arange(rounds)[::-1])
        decay_miss = np.sum(col * decay_weights)
        
        # Acceleration (second derivative)
        mr0 = col[-3:].mean()
        mr1 = col[-4:-1].mean() if rounds >= 4 else mr0
        mr2 = col[-5:-2].mean() if rounds >= 5 else mr1
        acceleration = mr0 - 2 * mr1 + mr2
        
        # Frequency
        frequency = 1 - col.mean()
        
        # Volatility
        volatility = np.std(col) if rounds > 1 else 0
        
        # Last gap
        last_gap = rounds
        for i in range(rounds-1, -1, -1):
            if col[i] == 0:
                last_gap = rounds - 1 - i
                break
        
        features[f"rolling_miss_{num}"] = rolling_miss
        features[f"decay_miss_{num}"] = decay_miss
        features[f"acceleration_{num}"] = acceleration
        features[f"frequency_{num}"] = frequency
        features[f"volatility_{num}"] = volatility
        features[f"last_gap_{num}"] = last_gap
    
    return features


def analyze_block3_pattern(draws):
    """Analyze block 3 pattern: 29 recent + 1 prediction = 30 draws = 10 blocks."""
    if len(draws) < 29:
        return {}
    
    # Take 29 recent draws + placeholder for prediction
    recent_29 = draws[-29:]
    all_30_draws = recent_29 + [[]]  # Empty list as placeholder
    
    patterns = {}
    
    # Analyze 10 blocks of 3 draws each
    for block_idx in range(10):
        start_idx = block_idx * 3
        block = all_30_draws[start_idx:start_idx + 3]
        
        for pos, draw in enumerate(block):
            position = pos + 1  # Position 1, 2, 3
            
            for num in draw:
                if 1 <= num <= 80:
                    if num not in patterns:
                        patterns[num] = {
                            'positions': [0, 0, 0],  # Count at position 1, 2, 3
                            'total_count': 0
                        }
                    
                    patterns[num]['positions'][position - 1] += 1
                    patterns[num]['total_count'] += 1
    
    # Calculate metrics for each number
    result = {}
    for num in range(1, 81):
        if num not in patterns:
            patterns[num] = {'positions': [0, 0, 0], 'total_count': 0}
        
        pos_counts = patterns[num]['positions']
        total = patterns[num]['total_count']
        
        # Position 3 ratio (prediction position)
        pos3_ratio = pos_counts[2] / max(1, total)
        pos3_count = pos_counts[2]
        
        # Dominant position
        dominant_position = np.argmax(pos_counts) + 1 if total > 0 else 0
        
        # Block frequency
        block_frequency = total / 10.0
        
        # High position 3 probability (if appears >= 3 times at position 3)
        high_pos3_probability = 1 if pos3_count >= 3 else 0
        
        result[num] = {
            'pos3_ratio': pos3_ratio,
            'pos3_count': pos3_count,
            'dominant_position': dominant_position,
            'block_frequency': block_frequency,
            'high_pos3_probability': high_pos3_probability
        }
    
    return result


def generate_multi_window_features(draws):
    """Generate features from multiple time windows."""
    features = {}
    
    # Different time windows
    windows = [3, 12, 30, 70]
    
    for window in windows:
        if len(draws) >= window:
            window_draws = draws[-window:]
            window_features = extract_basic_features(window_draws, window)
            
            if window_features:
                # Add window suffix to feature names
                for key, value in window_features.items():
                    features[f"{key}_w{window}"] = value
    
    return features


def generate_training_data_stream(data_list, window_size=70, output_file="training_features.pkl"):
    """Generate training data with TRUE ZERO RAM - direct CSV streaming."""
    print(f"Generating training data with window size {window_size}")
    print(f"Total available records: {len(data_list)}")

    if len(data_list) < window_size + 1:
        raise ValueError(f"Not enough data. Need at least {window_size + 1} records")

    # Calculate total samples
    total_samples = len(data_list) - window_size
    print(f"Will generate {total_samples} training samples")

    # First pass: determine feature structure
    print("Determining feature structure...")
    hist_data = data_list[0:window_size]
    next_data = data_list[window_size]
    hist_draws = [item['results'] for item in hist_data]
    next_result = set(next_data['results'])

    # Get sample features to determine structure
    sample_features = generate_multi_window_features(hist_draws)
    if len(hist_draws) >= 29:
        block3_patterns = analyze_block3_pattern(hist_draws)
        for num in range(1, 81):
            pattern = block3_patterns[num]
            sample_features[f"block3_pos3_ratio_{num}"] = pattern['pos3_ratio']
            sample_features[f"block3_pos3_count_{num}"] = pattern['pos3_count']
            sample_features[f"block3_dominant_pos_{num}"] = pattern['dominant_position']
            sample_features[f"block3_frequency_{num}"] = pattern['block_frequency']
            sample_features[f"block3_high_pos3_{num}"] = pattern['high_pos3_probability']

    feature_names = list(sample_features.keys())
    label_names = [f"label_{num}" for num in range(1, 81)]
    all_columns = feature_names + label_names

    print(f"Features per sample: {len(feature_names)}")
    print(f"Labels per sample: {len(label_names)}")

    # Create CSV file for streaming - NO RAM ACCUMULATION
    csv_file = output_file.replace('.pkl', '.csv')

    with open(csv_file, 'w', newline='') as f:
        import csv
        writer = csv.writer(f)

        # Write header
        writer.writerow(all_columns)

        # Process samples one by one - STREAM DIRECTLY TO FILE
        for i in tqdm(range(total_samples), desc="Streaming to CSV"):
            # Get historical data and next result
            hist_data = data_list[i:i + window_size]
            next_data = data_list[i + window_size]

            # Extract draws only
            hist_draws = [item['results'] for item in hist_data]
            next_result = set(next_data['results'])

            try:
                # Generate features
                features = generate_multi_window_features(hist_draws)

                # Add block 3 pattern features
                if len(hist_draws) >= 29:
                    block3_patterns = analyze_block3_pattern(hist_draws)
                    for num in range(1, 81):
                        pattern = block3_patterns[num]
                        features[f"block3_pos3_ratio_{num}"] = pattern['pos3_ratio']
                        features[f"block3_pos3_count_{num}"] = pattern['pos3_count']
                        features[f"block3_dominant_pos_{num}"] = pattern['dominant_position']
                        features[f"block3_frequency_{num}"] = pattern['block_frequency']
                        features[f"block3_high_pos3_{num}"] = pattern['high_pos3_probability']

                # Generate labels
                labels = {f"label_{num}": 1 if num not in next_result else 0 for num in range(1, 81)}

                # Create row data
                row_data = []
                for col_name in all_columns:
                    if col_name in features:
                        row_data.append(features[col_name])
                    elif col_name in labels:
                        row_data.append(labels[col_name])
                    else:
                        row_data.append(0)

                # Write row directly to CSV - NO RAM ACCUMULATION
                writer.writerow(row_data)

                # Clear variables immediately
                del features, labels, row_data, hist_draws, next_result

            except Exception as e:
                print(f"Error processing sample {i}: {e}")
                # Write zeros on error
                zero_row = [0] * len(all_columns)
                writer.writerow(zero_row)
                continue

    # Convert CSV to PKL in chunks - NEVER load full CSV to RAM
    print("Converting CSV to PKL format...")

    final_data = {
        'features': [],
        'labels': [],
        'sample_count': total_samples,
        'window_size': window_size,
        'generated_at': datetime.now().isoformat()
    }

    chunk_size = 1000
    processed_rows = 0

    with open(csv_file, 'r') as f:
        import csv
        reader = csv.reader(f)
        header = next(reader)  # Skip header

        chunk_features = []
        chunk_labels = []

        for row in tqdm(reader, desc="Converting CSV to PKL", total=total_samples):
            try:
                # Parse row
                feature_dict = {}
                label_dict = {}

                for i, value in enumerate(row):
                    col_name = header[i]
                    if col_name.startswith('label_'):
                        label_dict[col_name] = int(float(value))
                    else:
                        feature_dict[col_name] = float(value)

                chunk_features.append(feature_dict)
                chunk_labels.append(label_dict)
                processed_rows += 1

                # Save chunk when full
                if len(chunk_features) >= chunk_size:
                    final_data['features'].extend(chunk_features)
                    final_data['labels'].extend(chunk_labels)

                    # Save intermediate
                    temp_file = f"temp_{processed_rows}_{output_file}"
                    joblib.dump(final_data, temp_file)

                    # Clear chunk and reload
                    chunk_features = []
                    chunk_labels = []
                    del final_data
                    final_data = joblib.load(temp_file)

            except Exception as e:
                print(f"Error parsing row {processed_rows}: {e}")
                continue

        # Save remaining chunk
        if chunk_features:
            final_data['features'].extend(chunk_features)
            final_data['labels'].extend(chunk_labels)

    # Save final PKL file
    print(f"Saving final training data to {output_file}")
    joblib.dump(final_data, output_file)

    # Clean up CSV and temp files
    os.remove(csv_file)
    import glob
    for temp_file in glob.glob(f"temp_*_{output_file}"):
        os.remove(temp_file)

    print(f"Training data generation completed!")
    print(f"Generated {total_samples} samples")
    print(f"Features per sample: {len(feature_names)}")
    print(f"Saved to: {output_file}")

    return total_samples


def main():
    print("=== KENO TRAINING DATA GENERATOR ===")

    # Load data from database
    data_list = load_data_from_db()

    if len(data_list) < 100:
        print("ERROR: Not enough data in database")
        return

    # Generate training data with streaming approach
    sample_count = generate_training_data_stream(
        data_list,
        window_size=70,
        output_file="training_features.pkl"
    )

    print(f"Data generation completed successfully! Generated {sample_count} samples")


if __name__ == "__main__":
    main()
