import pymysql
import pandas as pd
import numpy as np
from tqdm import tqdm
import joblib
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Database configuration
DB_CFG = dict(
    host="127.0.0.1",
    user="root",
    password="",
    db="lstx",
    charset="utf8mb4",
    cursorclass=pymysql.cursors.DictCursor
)


def load_data_from_db():
    """Load all data from database."""
    print("Connecting to database...")
    conn = pymysql.connect(**DB_CFG)
    cursor = conn.cursor()
    
    print("Loading data from history_kennos...")
    cursor.execute("SELECT period, date, time, results FROM history_kennos ORDER BY period ASC")
    rows = cursor.fetchall()
    conn.close()
    
    print(f"Loaded {len(rows)} records from database")
    
    # Convert to structured data
    data_list = []
    for row in rows:
        try:
            # Parse results string to list of integers
            results = [int(x.strip()) for x in row['results'].split(',')]
            
            data_item = {
                'period': row['period'],
                'date': str(row['date']) if row['date'] else '',
                'time': str(row['time']) if row['time'] else '',
                'results': results
            }
            data_list.append(data_item)
        except Exception as e:
            print(f"Error parsing row {row['period']}: {e}")
            continue
    
    print(f"Successfully parsed {len(data_list)} records")
    return data_list


def extract_basic_features(draws, window_size=70):
    """Extract basic features for single window."""
    if len(draws) < 3:
        return None
    
    rounds = len(draws)
    features = {}
    
    # Create miss matrix (1 = miss, 0 = hit)
    miss_mat = np.ones((rounds, 80), dtype=int)
    for r, draw in enumerate(draws):
        for n in draw:
            if 1 <= n <= 80:
                miss_mat[r, n - 1] = 0
    
    # Extract features for each number
    for num in range(1, 81):
        col = miss_mat[:, num - 1]
        
        # Rolling miss rate (last 3 rounds)
        rolling_miss = col[-3:].mean()
        
        # Decay weighted miss
        decay_weights = np.exp(-0.5 * np.arange(rounds)[::-1])
        decay_miss = np.sum(col * decay_weights)
        
        # Acceleration (second derivative)
        mr0 = col[-3:].mean()
        mr1 = col[-4:-1].mean() if rounds >= 4 else mr0
        mr2 = col[-5:-2].mean() if rounds >= 5 else mr1
        acceleration = mr0 - 2 * mr1 + mr2
        
        # Frequency
        frequency = 1 - col.mean()
        
        # Volatility
        volatility = np.std(col) if rounds > 1 else 0
        
        # Last gap
        last_gap = rounds
        for i in range(rounds-1, -1, -1):
            if col[i] == 0:
                last_gap = rounds - 1 - i
                break
        
        features[f"rolling_miss_{num}"] = rolling_miss
        features[f"decay_miss_{num}"] = decay_miss
        features[f"acceleration_{num}"] = acceleration
        features[f"frequency_{num}"] = frequency
        features[f"volatility_{num}"] = volatility
        features[f"last_gap_{num}"] = last_gap
    
    return features


def analyze_block3_pattern(draws):
    """Analyze block 3 pattern: 29 recent + 1 prediction = 30 draws = 10 blocks."""
    if len(draws) < 29:
        return {}
    
    # Take 29 recent draws + placeholder for prediction
    recent_29 = draws[-29:]
    all_30_draws = recent_29 + [[]]  # Empty list as placeholder
    
    patterns = {}
    
    # Analyze 10 blocks of 3 draws each
    for block_idx in range(10):
        start_idx = block_idx * 3
        block = all_30_draws[start_idx:start_idx + 3]
        
        for pos, draw in enumerate(block):
            position = pos + 1  # Position 1, 2, 3
            
            for num in draw:
                if 1 <= num <= 80:
                    if num not in patterns:
                        patterns[num] = {
                            'positions': [0, 0, 0],  # Count at position 1, 2, 3
                            'total_count': 0
                        }
                    
                    patterns[num]['positions'][position - 1] += 1
                    patterns[num]['total_count'] += 1
    
    # Calculate metrics for each number
    result = {}
    for num in range(1, 81):
        if num not in patterns:
            patterns[num] = {'positions': [0, 0, 0], 'total_count': 0}
        
        pos_counts = patterns[num]['positions']
        total = patterns[num]['total_count']
        
        # Position 3 ratio (prediction position)
        pos3_ratio = pos_counts[2] / max(1, total)
        pos3_count = pos_counts[2]
        
        # Dominant position
        dominant_position = np.argmax(pos_counts) + 1 if total > 0 else 0
        
        # Block frequency
        block_frequency = total / 10.0
        
        # High position 3 probability (if appears >= 3 times at position 3)
        high_pos3_probability = 1 if pos3_count >= 3 else 0
        
        result[num] = {
            'pos3_ratio': pos3_ratio,
            'pos3_count': pos3_count,
            'dominant_position': dominant_position,
            'block_frequency': block_frequency,
            'high_pos3_probability': high_pos3_probability
        }
    
    return result


def generate_multi_window_features(draws):
    """Generate features from multiple time windows."""
    features = {}
    
    # Different time windows
    windows = [3, 12, 30, 70]
    
    for window in windows:
        if len(draws) >= window:
            window_draws = draws[-window:]
            window_features = extract_basic_features(window_draws, window)
            
            if window_features:
                # Add window suffix to feature names
                for key, value in window_features.items():
                    features[f"{key}_w{window}"] = value
    
    return features


def generate_training_data(data_list, window_size=70, output_file="training_features.pkl"):
    """Generate training data and save to file with memory optimization."""
    print(f"Generating training data with window size {window_size}")
    print(f"Total available records: {len(data_list)}")

    if len(data_list) < window_size + 1:
        raise ValueError(f"Not enough data. Need at least {window_size + 1} records")

    # Calculate total samples
    total_samples = len(data_list) - window_size
    print(f"Will generate {total_samples} training samples")

    # Process in smaller batches and save immediately
    batch_size = 500  # Reduced batch size
    save_batch_size = 2000  # Save every 2000 samples

    total_processed = 0
    batch_files = []

    for batch_start in range(0, total_samples, save_batch_size):
        batch_end = min(batch_start + save_batch_size, total_samples)

        print(f"Processing mega-batch {batch_start//save_batch_size + 1}/{(total_samples-1)//save_batch_size + 1}")

        # Process this mega-batch
        mega_batch_features = []
        mega_batch_labels = []

        for sub_batch_start in range(batch_start, batch_end, batch_size):
            sub_batch_end = min(sub_batch_start + batch_size, batch_end)

            print(f"  Sub-batch: {sub_batch_start} to {sub_batch_end}")

            for i in tqdm(range(sub_batch_start, sub_batch_end), desc="Processing", leave=False):
                # Get historical data and next result
                hist_data = data_list[i:i + window_size]
                next_data = data_list[i + window_size]

                # Extract draws only
                hist_draws = [item['results'] for item in hist_data]
                next_result = set(next_data['results'])

                try:
                    # Generate multi-window features
                    features = generate_multi_window_features(hist_draws)

                    # Add block 3 pattern features
                    if len(hist_draws) >= 29:
                        block3_patterns = analyze_block3_pattern(hist_draws)
                        for num in range(1, 81):
                            pattern = block3_patterns[num]
                            features[f"block3_pos3_ratio_{num}"] = pattern['pos3_ratio']
                            features[f"block3_pos3_count_{num}"] = pattern['pos3_count']
                            features[f"block3_dominant_pos_{num}"] = pattern['dominant_position']
                            features[f"block3_frequency_{num}"] = pattern['block_frequency']
                            features[f"block3_high_pos3_{num}"] = pattern['high_pos3_probability']

                    # Generate labels (1 = miss, 0 = hit)
                    labels = {}
                    for num in range(1, 81):
                        labels[f"label_{num}"] = 1 if num not in next_result else 0

                    mega_batch_features.append(features)
                    mega_batch_labels.append(labels)

                except Exception as e:
                    print(f"Error processing sample {i}: {e}")
                    continue

        # Save this mega-batch to separate file
        if mega_batch_features:
            batch_filename = f"batch_{len(batch_files):03d}_{output_file}"
            batch_data = {
                'features': mega_batch_features,
                'labels': mega_batch_labels,
                'sample_count': len(mega_batch_features),
                'batch_start': batch_start,
                'batch_end': batch_end
            }

            print(f"Saving batch to {batch_filename} ({len(mega_batch_features)} samples)")
            joblib.dump(batch_data, batch_filename)
            batch_files.append(batch_filename)

            total_processed += len(mega_batch_features)

            # Clear memory
            del mega_batch_features
            del mega_batch_labels
            del batch_data

    # Combine all batch files into final file
    print(f"Combining {len(batch_files)} batch files into final file...")

    final_features = []
    final_labels = []

    for i, batch_file in enumerate(batch_files):
        print(f"Loading batch {i+1}/{len(batch_files)}: {batch_file}")
        batch_data = joblib.load(batch_file)

        final_features.extend(batch_data['features'])
        final_labels.extend(batch_data['labels'])

        # Clean up batch file
        os.remove(batch_file)
        del batch_data

    # Save final results
    print(f"Saving final training data to {output_file}")
    training_data = {
        'features': final_features,
        'labels': final_labels,
        'sample_count': len(final_features),
        'window_size': window_size,
        'generated_at': datetime.now().isoformat()
    }

    joblib.dump(training_data, output_file)

    print(f"Training data generation completed!")
    print(f"Generated {len(final_features)} samples")
    print(f"Features per sample: {len(final_features[0]) if final_features else 0}")
    print(f"Saved to: {output_file}")

    return len(final_features)


def main():
    print("=== KENO TRAINING DATA GENERATOR ===")

    # Load data from database
    data_list = load_data_from_db()

    if len(data_list) < 100:
        print("ERROR: Not enough data in database")
        return

    # Generate training data
    sample_count = generate_training_data(
        data_list,
        window_size=70,
        output_file="training_features.pkl"
    )

    print(f"Data generation completed successfully! Generated {sample_count} samples")


if __name__ == "__main__":
    main()
