import joblib
import pandas as pd
import numpy as np
from flask import Flask, request, jsonify
import pymysql
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

app = Flask(__name__)

# Database configuration
DB_CFG = dict(
    host="127.0.0.1",
    user="root",
    password="",
    db="lstx",
    charset="utf8mb4",
    cursorclass=pymysql.cursors.DictCursor
)

# Global variables
models = None
feature_columns = None

def load_models(model_file="simple_models.pkl"):
    """Load trained models."""
    global models, feature_columns
    
    try:
        print(f"Loading models from {model_file}")
        model_data = joblib.load(model_file)
        models = model_data['models']
        
        # Get feature columns from first model
        if models:
            first_model = list(models.values())[0]
            feature_columns = first_model['feature_columns']
        
        print(f"Loaded {len(models)} models")
        print(f"Feature columns: {len(feature_columns)}")
        return True
        
    except Exception as e:
        print(f"Error loading models: {e}")
        return False

def get_recent_data(limit=70):
    """Get recent data from database."""
    try:
        conn = pymysql.connect(**DB_CFG)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT period, date, time, results 
            FROM history_kennos 
            ORDER BY period DESC 
            LIMIT %s
        """, (limit,))
        
        rows = cursor.fetchall()
        conn.close()
        
        # Convert to structured data (reverse to get chronological order)
        data_list = []
        for row in reversed(rows):
            try:
                results = [int(x.strip()) for x in row['results'].split(',')]
                data_item = {
                    'period': row['period'],
                    'date': str(row['date']) if row['date'] else '',
                    'time': str(row['time']) if row['time'] else '',
                    'results': results
                }
                data_list.append(data_item)
            except Exception as e:
                print(f"Error parsing row {row['period']}: {e}")
                continue
        
        return data_list
        
    except Exception as e:
        print(f"Error getting recent data: {e}")
        return []

def extract_basic_features(draws, window_size=70):
    """Extract basic features (same as training)."""
    if len(draws) < 3:
        return None
    
    rounds = len(draws)
    features = {}
    
    # Create miss matrix (1 = miss, 0 = hit)
    miss_mat = np.ones((rounds, 80), dtype=int)
    for r, draw in enumerate(draws):
        for n in draw:
            if 1 <= n <= 80:
                miss_mat[r, n - 1] = 0
    
    # Extract features for each number
    for num in range(1, 81):
        col = miss_mat[:, num - 1]
        
        # Rolling miss rate (last 3 rounds)
        rolling_miss = col[-3:].mean()
        
        # Decay weighted miss
        decay_weights = np.exp(-0.5 * np.arange(rounds)[::-1])
        decay_miss = np.sum(col * decay_weights)
        
        # Recent frequency
        recent_freq = np.sum(1 - col[-10:]) if rounds >= 10 else np.sum(1 - col)
        
        # Features
        features[f"rolling_miss_{num}"] = rolling_miss
        features[f"decay_miss_{num}"] = decay_miss
        features[f"recent_freq_{num}"] = recent_freq
        features[f"total_miss_{num}"] = np.sum(col)
    
    return features

@app.route('/predict', methods=['GET', 'POST'])
def predict():
    """Make prediction for next draw."""
    global models, feature_columns
    
    if models is None:
        return jsonify({'error': 'Models not loaded'}), 500
    
    try:
        # Get recent data
        recent_data = get_recent_data(70)
        
        if len(recent_data) < 70:
            return jsonify({'error': 'Not enough historical data'}), 400
        
        # Extract features
        draws = [item['results'] for item in recent_data]
        features = extract_basic_features(draws, 70)
        
        if features is None:
            return jsonify({'error': 'Failed to extract features'}), 400
        
        # Convert to DataFrame
        features_df = pd.DataFrame([features])
        
        # Ensure all feature columns are present
        for col in feature_columns:
            if col not in features_df.columns:
                features_df[col] = 0
        
        # Reorder columns to match training
        features_df = features_df[feature_columns]
        
        # Make predictions
        predictions = {}
        miss_probabilities = {}
        
        for num in range(1, 81):
            if num in models:
                model_info = models[num]
                model = model_info['model']
                
                # Predict probability of miss (1)
                miss_prob = model.predict_proba(features_df)[0][1]
                miss_probabilities[num] = float(miss_prob)
                
                # Predict miss (1) or hit (0)
                prediction = model.predict(features_df)[0]
                predictions[num] = int(prediction)
        
        # Get top 10 numbers most likely to be missed
        sorted_miss = sorted(miss_probabilities.items(), key=lambda x: x[1], reverse=True)
        top_miss_numbers = [num for num, prob in sorted_miss[:10]]
        
        # Get top 10 numbers most likely to hit
        sorted_hit = sorted(miss_probabilities.items(), key=lambda x: x[1])
        top_hit_numbers = [num for num, prob in sorted_hit[:10]]
        
        result = {
            'success': True,
            'predictions': predictions,
            'miss_probabilities': miss_probabilities,
            'top_miss_numbers': top_miss_numbers,
            'top_hit_numbers': top_hit_numbers,
            'prediction_time': datetime.now().isoformat(),
            'model_type': 'simple_gbm_daily_grouped'
        }
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/status', methods=['GET'])
def status():
    """Get API status."""
    global models, feature_columns
    
    return jsonify({
        'status': 'running',
        'models_loaded': models is not None,
        'model_count': len(models) if models else 0,
        'feature_count': len(feature_columns) if feature_columns else 0,
        'model_type': 'simple_gbm_daily_grouped'
    })

def main():
    print("=== KENO PREDICTION API (SIMPLE + DAILY GROUPING) ===")
    
    # Load models
    if not load_models("simple_models.pkl"):
        print("Failed to load models!")
        return
    
    print("Starting Keno Prediction API...")
    print(f"Available endpoints:")
    print(f"  GET  /status  - API status")
    print(f"  POST /predict - Make prediction")
    print(f"Total models: {len(models)}")
    print(f"Total features: {len(feature_columns)}")
    
    # Start Flask app
    app.run(host='127.0.0.1', port=5049, debug=True)

if __name__ == "__main__":
    main()
