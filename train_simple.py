import joblib
import pandas as pd
import numpy as np
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

def load_training_data(file_path="training_features.pkl"):
    """Load training data from file."""
    print(f"Loading training data from {file_path}")
    
    try:
        data = joblib.load(file_path)
        print(f"Loaded {data['sample_count']} samples")
        print(f"Window size: {data['window_size']}")
        print(f"Daily grouped: {data.get('daily_grouped', False)}")
        return data
    except Exception as e:
        print(f"Error loading training data: {e}")
        return None

def prepare_data(data):
    """Prepare data for training."""
    features_list = data['features']
    labels_list = data['labels']
    
    print("Converting to DataFrame...")
    
    # Convert features to DataFrame
    features_df = pd.DataFrame(features_list)
    
    # Convert labels to DataFrame
    labels_df = pd.DataFrame(labels_list)
    
    print(f"Features shape: {features_df.shape}")
    print(f"Labels shape: {labels_df.shape}")
    
    return features_df, labels_df

def train_models(features_df, labels_df):
    """Train simple GBM models for each number."""
    print("Training GBM models...")
    
    models = {}
    feature_columns = list(features_df.columns)
    
    # Train model for each number
    for num in tqdm(range(1, 81), desc="Training models"):
        label_col = f"label_{num}"
        
        if label_col not in labels_df.columns:
            continue
            
        y = labels_df[label_col]
        X = features_df
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Train GBM
        model = GradientBoostingClassifier(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=3,
            random_state=42
        )
        
        model.fit(X_train, y_train)
        
        # Evaluate
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        models[num] = {
            'model': model,
            'accuracy': accuracy,
            'feature_columns': feature_columns
        }
    
    print(f"Trained {len(models)} models")
    return models

def evaluate_models(models):
    """Evaluate model performance."""
    print("\n=== MODEL EVALUATION ===")
    
    accuracies = [info['accuracy'] for info in models.values()]
    
    print(f"Average accuracy: {np.mean(accuracies):.4f}")
    print(f"Min accuracy: {np.min(accuracies):.4f}")
    print(f"Max accuracy: {np.max(accuracies):.4f}")
    print(f"Std accuracy: {np.std(accuracies):.4f}")
    
    # Show best and worst performing numbers
    sorted_models = sorted(models.items(), key=lambda x: x[1]['accuracy'], reverse=True)
    
    print("\nTop 5 best performing numbers:")
    for num, info in sorted_models[:5]:
        print(f"  Number {num}: {info['accuracy']:.4f}")
    
    print("\nTop 5 worst performing numbers:")
    for num, info in sorted_models[-5:]:
        print(f"  Number {num}: {info['accuracy']:.4f}")

def save_models(models, output_file="simple_models.pkl"):
    """Save trained models."""
    print(f"Saving models to {output_file}")
    
    model_data = {
        'models': models,
        'model_type': 'simple_gbm',
        'daily_grouped': True,
        'trained_at': pd.Timestamp.now().isoformat()
    }
    
    joblib.dump(model_data, output_file)
    print(f"Models saved successfully!")

def main():
    print("=== KENO MODEL TRAINER (SIMPLE + DAILY GROUPING) ===")
    
    # Load training data
    data = load_training_data("training_features.pkl")
    if data is None:
        print("Failed to load training data!")
        return
    
    # Prepare data
    features_df, labels_df = prepare_data(data)
    
    # Train models
    models = train_models(features_df, labels_df)
    
    # Evaluate models
    evaluate_models(models)
    
    # Save models
    save_models(models, "simple_models.pkl")
    
    print("\nTraining completed successfully!")

if __name__ == "__main__":
    main()
