import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import roc_auc_score, accuracy_score
from tqdm import tqdm
import joblib
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')


class ModelTrainer:
    """Train ensemble models for Keno prediction."""
    
    def __init__(self):
        self.models = {}
        self.feature_columns = []
        self.training_results = {}
    
    def load_training_data(self, data_file="training_features.pkl"):
        """Load training data from file."""
        print(f"Loading training data from {data_file}")
        
        if not os.path.exists(data_file):
            raise FileNotFoundError(f"Training data file not found: {data_file}")
        
        data = joblib.load(data_file)
        
        features_list = data['features']
        labels_list = data['labels']
        
        print(f"Loaded {len(features_list)} training samples")
        
        # Convert to DataFrames
        print("Converting to DataFrames...")
        X = pd.DataFrame(features_list)
        
        # Create labels DataFrame
        y_data = {}
        for num in range(1, 81):
            label_key = f"label_{num}"
            y_data[label_key] = [labels[label_key] for labels in labels_list]
        
        y = pd.DataFrame(y_data)
        
        print(f"Features shape: {X.shape}")
        print(f"Labels shape: {y.shape}")
        
        # Save feature columns
        self.feature_columns = X.columns.tolist()
        with open('feature_columns.txt', 'w') as f:
            for col in self.feature_columns:
                f.write(col + '\n')
        
        return X, y
    
    def train_strategy_models(self, X, y, strategy_name, params):
        """Train models for a specific strategy."""
        print(f"Training {strategy_name} strategy...")
        
        models = {}
        auc_scores = []
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        print(f"Train samples: {len(X_train)}")
        print(f"Test samples: {len(X_test)}")
        
        # Train model for each number
        for num in tqdm(range(1, 81), desc=f"Training {strategy_name}"):
            label_key = f"label_{num}"
            
            y_tr = y_train[label_key]
            y_te = y_test[label_key]
            
            # Check class balance
            if len(np.unique(y_tr)) < 2:
                print(f"Skipping {label_key} - insufficient class diversity")
                continue
            
            # Create and train model
            model = lgb.LGBMClassifier(
                objective='binary',
                boosting_type='gbdt',
                random_state=42,
                verbose=-1,
                **params
            )
            
            try:
                model.fit(
                    X_train, y_tr,
                    eval_set=[(X_test, y_te)],
                    callbacks=[lgb.early_stopping(20)],
                    eval_metric='auc'
                )
                
                # Evaluate
                preds = model.predict_proba(X_test)[:, 1]
                auc = roc_auc_score(y_te, preds)
                auc_scores.append(auc)
                
                models[label_key] = model
                
            except Exception as e:
                print(f"Error training {strategy_name} model for {label_key}: {e}")
                continue
        
        mean_auc = np.mean(auc_scores) if auc_scores else 0
        print(f"{strategy_name} strategy - Mean AUC: {mean_auc:.4f}")
        print(f"Successfully trained {len(models)} models")
        
        return models, mean_auc
    
    def train_ensemble_models(self, X, y):
        """Train ensemble models with different strategies."""
        print("Training ensemble models...")
        
        # Define strategies
        strategies = {
            'conservative': {
                'n_estimators': 200,
                'learning_rate': 0.03,
                'num_leaves': 15,
                'min_data_in_leaf': 30,
                'feature_fraction': 0.8,
                'bagging_fraction': 0.8,
                'bagging_freq': 5
            },
            'aggressive': {
                'n_estimators': 300,
                'learning_rate': 0.08,
                'num_leaves': 63,
                'min_data_in_leaf': 10,
                'feature_fraction': 0.9,
                'bagging_fraction': 0.9,
                'bagging_freq': 3
            },
            'balanced': {
                'n_estimators': 250,
                'learning_rate': 0.05,
                'num_leaves': 31,
                'min_data_in_leaf': 20,
                'feature_fraction': 0.85,
                'bagging_fraction': 0.85,
                'bagging_freq': 4
            }
        }
        
        ensemble_models = {}
        strategy_results = {}
        
        # Train each strategy
        for strategy_name, params in strategies.items():
            models, mean_auc = self.train_strategy_models(X, y, strategy_name, params)
            
            if models:
                ensemble_models[strategy_name] = models
                strategy_results[strategy_name] = {
                    'mean_auc': mean_auc,
                    'model_count': len(models)
                }
        
        self.models = ensemble_models
        self.training_results = {
            'strategies': strategy_results,
            'total_features': len(X.columns),
            'total_samples': len(X),
            'trained_at': datetime.now().isoformat()
        }
        
        return ensemble_models, strategy_results
    
    def save_models(self, model_file="ensemble_models.pkl", results_file="training_results.pkl"):
        """Save trained models and results."""
        print(f"Saving models to {model_file}")
        joblib.dump(self.models, model_file)
        
        print(f"Saving training results to {results_file}")
        joblib.dump(self.training_results, results_file)
        
        print("Models saved successfully!")
    
    def evaluate_models(self, X, y, test_samples=100):
        """Evaluate ensemble models."""
        print("Evaluating ensemble models...")
        
        if not self.models:
            print("No models to evaluate")
            return
        
        # Take a subset for evaluation
        X_eval = X.tail(test_samples)
        y_eval = y.tail(test_samples)
        
        strategy_accuracies = {}
        
        for strategy_name, models in self.models.items():
            print(f"Evaluating {strategy_name} strategy...")
            
            correct_predictions = 0
            total_predictions = 0
            
            for idx in tqdm(range(len(X_eval)), desc=f"Testing {strategy_name}"):
                sample_X = X_eval.iloc[idx:idx+1]
                sample_y = y_eval.iloc[idx]
                
                # Get predictions for all numbers
                predictions = {}
                for num in range(1, 81):
                    label_key = f"label_{num}"
                    if label_key in models:
                        try:
                            pred_prob = models[label_key].predict_proba(sample_X)[:, 1][0]
                            predictions[num] = pred_prob
                        except:
                            predictions[num] = 0.5
                    else:
                        predictions[num] = 0.5
                
                # Calculate accuracy
                for num in range(1, 81):
                    predicted_miss = predictions[num] > 0.5
                    actual_miss = sample_y[f"label_{num}"] == 1
                    
                    if predicted_miss == actual_miss:
                        correct_predictions += 1
                    total_predictions += 1
            
            accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
            strategy_accuracies[strategy_name] = accuracy
            print(f"{strategy_name} accuracy: {accuracy:.4f}")
        
        return strategy_accuracies


def main():
    print("=== KENO MODEL TRAINER ===")
    
    # Check if training data exists
    if not os.path.exists("training_features.pkl"):
        print("ERROR: Training data not found. Please run data_generator.py first")
        return
    
    # Initialize trainer
    trainer = ModelTrainer()
    
    # Load training data
    try:
        X, y = trainer.load_training_data("training_features.pkl")
    except Exception as e:
        print(f"ERROR loading training data: {e}")
        return
    
    # Train ensemble models
    try:
        ensemble_models, strategy_results = trainer.train_ensemble_models(X, y)
        
        print("\n=== TRAINING RESULTS ===")
        for strategy, results in strategy_results.items():
            print(f"{strategy}: AUC={results['mean_auc']:.4f}, Models={results['model_count']}")
        
    except Exception as e:
        print(f"ERROR during training: {e}")
        return
    
    # Save models
    trainer.save_models()
    
    # Evaluate models
    try:
        accuracies = trainer.evaluate_models(X, y, test_samples=50)
        print("\n=== EVALUATION RESULTS ===")
        for strategy, accuracy in accuracies.items():
            print(f"{strategy} accuracy: {accuracy:.4f}")
    except Exception as e:
        print(f"ERROR during evaluation: {e}")
    
    print("\nModel training completed successfully!")


if __name__ == "__main__":
    main()
