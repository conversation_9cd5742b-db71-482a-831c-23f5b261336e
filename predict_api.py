import pandas as pd
import joblib
import lightgbm as lgb
from flask import Flask, request, jsonify
from feature_engineering import extract_features
from enhanced_feature_engineering import extract_enhanced_features
from dynamic_pattern_learning import DynamicPatternLearning
from datetime import datetime
import os

app = Flask(__name__)

# Khởi tạo hệ thống dynamic pattern learning
dynamic_system = DynamicPatternLearning()

# Load trained models (fallback)
try:
    fallback_models = joblib.load("multi_label_models.pkl")
    print("Loaded fallback models")
except:
    fallback_models = {}
    print("No fallback models found")

@app.route("/predict-10-number", methods=["POST"])
def predict():
    try:
        data = request.json

        # Hỗ trợ nhiều tên tham số
        draws = data.get("draws") or data.get("draws_70") or data.get("history")
        timestamp = data.get("timestamp")

        if not draws or len(draws) < 10:
            return jsonify({
                "error": "Input must have at least 30 rounds.",
                "received": len(draws) if draws else 0,
                "required": 30
            }), 400

        # Tạo timestamp nếu không có
        if not timestamp:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Dự đoán với dynamic pattern system
        try:
            preds = dynamic_system.predict_with_ensemble(draws)
            prediction_source = "dynamic_pattern"
        except Exception as e:
            print(f"Dynamic pattern prediction failed: {e}")

            # Fallback to original method
            try:
                df_feat = extract_features(draws)
                X = df_feat.iloc[:, :240] if df_feat.shape[1] >= 240 else df_feat

                preds = {}
                for label, model in fallback_models.items():
                    prob = model.predict_proba(X)[:, 1][0]
                    index = int(label.split("_")[-1])
                    preds[index] = prob

                prediction_source = "fallback"
            except Exception as e2:
                return jsonify({"error": f"Both prediction methods failed: {str(e)}, {str(e2)}"}), 500

        # Áp dụng block 3 pattern enhancement
        if len(draws) >= 29:
            try:
                from enhanced_feature_engineering import analyze_block3_pattern
                block3_patterns = analyze_block3_pattern(draws)

                for num in range(1, 81):
                    if num in block3_patterns:
                        pattern = block3_patterns[num]
                        # Nếu số này xuất hiện >= 3 lần ở vị trí 3, giảm xác suất trượt
                        if pattern['high_pos3_probability'] == 1:
                            preds[num] = preds.get(num, 0.5) * 0.7  # Giảm 30% xác suất trượt
            except Exception as e:
                print(f"Block3 pattern enhancement failed: {e}")

        # Sort by highest miss probability
        top20 = sorted(preds.items(), key=lambda x: -x[1])[:20]
        top20_numbers = [i[0] for i in top20]

        # Lưu prediction để học liên tục
        prediction_id = timestamp

        return jsonify({
            "top20_miss_numbers": top20_numbers,
            "prediction_id": prediction_id,
            "prediction_source": prediction_source,
            "timestamp": timestamp,
            "total_draws": len(draws)
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/update-result", methods=["POST"])
def update_result():
    """API để cập nhật kết quả thực tế và trigger adaptive learning."""
    try:
        data = request.json

        prediction_id = data.get("prediction_id")
        actual_result = data.get("actual_result")
        draws = data.get("draws") or data.get("draws_70") or data.get("history")

        if not prediction_id or not actual_result or not draws:
            return jsonify({
                "error": "Missing required fields: prediction_id, actual_result, draws"
            }), 400

        # Validate actual_result
        if not isinstance(actual_result, list) or len(actual_result) == 0:
            return jsonify({
                "error": "actual_result must be a non-empty list of numbers"
            }), 400

        # Lấy predictions từ cache hoặc tính lại
        try:
            # Tính lại predictions để có baseline
            predictions = dynamic_system.predict_with_ensemble(draws)
        except Exception as e:
            return jsonify({
                "error": f"Could not recreate predictions: {str(e)}"
            }), 500

        # Cập nhật dynamic system
        try:
            dynamic_system.update_with_result(draws, actual_result, predictions)

            # Tính accuracy cho response
            correct_predictions = 0
            for num in range(1, 81):
                predicted_miss = predictions.get(num, 0.5) > 0.5
                actual_miss = num not in actual_result
                if predicted_miss == actual_miss:
                    correct_predictions += 1

            accuracy = correct_predictions / 80

            return jsonify({
                "message": "Result updated successfully",
                "accuracy": accuracy,
                "correct_predictions": correct_predictions,
                "total_predictions": 80,
                "prediction_id": prediction_id
            })

        except Exception as e:
            return jsonify({
                "error": f"Failed to update dynamic system: {str(e)}"
            }), 500

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/pattern-analysis", methods=["POST"])
def get_pattern_analysis():
    """API để phân tích patterns cho dữ liệu cụ thể."""
    try:
        data = request.json
        draws = data.get("draws") or data.get("draws_70") or data.get("history")

        if not draws or len(draws) < 10:
            return jsonify({"error": "Need at least 10 draws for pattern analysis"}), 400

        # Phân tích patterns
        seq_patterns = dynamic_system.extract_sequence_pattern(draws)
        hot_cold = dynamic_system.detect_hot_cold_cycles(draws)
        freq_momentum = dynamic_system.calculate_frequency_momentum(draws)

        # Tóm tắt insights
        insights = []

        # Hot numbers
        hot_numbers = [num for num in range(1, 81)
                      if hot_cold[num]['status'] == 'hot' and hot_cold[num]['intensity'] > 0.3]
        if hot_numbers:
            insights.append(f"Hot numbers (high appearance): {hot_numbers[:10]}")

        # Cold numbers
        cold_numbers = [num for num in range(1, 81)
                       if hot_cold[num]['status'] == 'cold' and hot_cold[num]['intensity'] > 0.3]
        if cold_numbers:
            insights.append(f"Cold numbers (low appearance): {cold_numbers[:10]}")

        # Increasing trend
        increasing = [num for num in range(1, 81)
                     if freq_momentum[num]['trend_prediction'] == 'increasing']
        if increasing:
            insights.append(f"Numbers with increasing trend: {increasing[:10]}")

        return jsonify({
            "sequence_patterns": {str(k): v for k, v in list(seq_patterns.items())[:5]},
            "hot_cold_analysis": {str(k): v for k, v in list(hot_cold.items())[:5]},
            "frequency_momentum": {str(k): v for k, v in list(freq_momentum.items())[:5]},
            "insights": insights,
            "total_draws_analyzed": len(draws)
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/retrain-dynamic", methods=["POST"])
def retrain_dynamic():
    """API để retrain dynamic pattern system."""
    try:
        data = request.json
        min_samples = data.get("min_samples", 50)

        # Kiểm tra có đủ dữ liệu không
        if len(dynamic_system.recent_results) < min_samples:
            return jsonify({
                "error": f"Not enough training data. Have {len(dynamic_system.recent_results)}, need {min_samples}"
            }), 400

        # Retrain với recent results
        training_data = list(dynamic_system.recent_results)
        dynamic_system.train_ensemble_models(training_data)

        return jsonify({
            "message": "Dynamic pattern system retrained successfully",
            "training_samples": len(training_data),
            "strategies": list(dynamic_system.model_ensemble.keys())
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500


if __name__ == "__main__":
    app.run(debug=True, port=5049)
