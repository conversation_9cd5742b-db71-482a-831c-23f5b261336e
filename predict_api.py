import pandas as pd
import joblib
import numpy as np
from flask import Flask, request, jsonify
from datetime import datetime
import os
from data_generator import generate_multi_window_features, analyze_block3_pattern

app = Flask(__name__)

# Load trained ensemble models
try:
    ensemble_models = joblib.load("ensemble_models.pkl")
    print("Loaded ensemble models")
    print(f"Available strategies: {list(ensemble_models.keys())}")
except Exception as e:
    ensemble_models = {}
    print(f"No ensemble models found: {e}")

# Load feature columns
try:
    with open('feature_columns.txt', 'r') as f:
        feature_columns = [line.strip() for line in f.readlines()]
    print(f"Loaded {len(feature_columns)} feature columns")
except:
    feature_columns = []
    print("No feature columns found")

def predict_with_ensemble(draws):
    """Predict using ensemble models."""
    if not ensemble_models:
        return {num: 0.5 for num in range(1, 81)}

    # Generate features
    features = generate_multi_window_features(draws)

    # Add block 3 pattern features
    if len(draws) >= 29:
        block3_patterns = analyze_block3_pattern(draws)
        for num in range(1, 81):
            pattern = block3_patterns[num]
            features[f"block3_pos3_ratio_{num}"] = pattern['pos3_ratio']
            features[f"block3_pos3_count_{num}"] = pattern['pos3_count']
            features[f"block3_dominant_pos_{num}"] = pattern['dominant_position']
            features[f"block3_frequency_{num}"] = pattern['block_frequency']
            features[f"block3_high_pos3_{num}"] = pattern['high_pos3_probability']

    # Convert to DataFrame
    feature_df = pd.DataFrame([features])

    # Ensure all required columns exist
    for col in feature_columns:
        if col not in feature_df.columns:
            feature_df[col] = 0

    # Reorder columns to match training
    feature_df = feature_df[feature_columns]

    # Get predictions from all strategies
    ensemble_predictions = {}
    strategy_weights = {'conservative': 0.3, 'aggressive': 0.3, 'balanced': 0.4}

    for num in range(1, 81):
        label_key = f"label_{num}"
        predictions = []
        weights = []

        for strategy_name, models in ensemble_models.items():
            if label_key in models:
                try:
                    pred = models[label_key].predict_proba(feature_df)[:, 1][0]
                    predictions.append(pred)
                    weights.append(strategy_weights.get(strategy_name, 1.0))
                except Exception as e:
                    print(f"Error predicting with {strategy_name} for {label_key}: {e}")
                    continue

        if predictions:
            # Weighted average
            final_pred = sum(p * w for p, w in zip(predictions, weights)) / sum(weights)

            # Apply block 3 pattern enhancement
            if len(draws) >= 29 and num in block3_patterns:
                pattern = block3_patterns[num]
                if pattern['high_pos3_probability'] == 1:
                    final_pred *= 0.7  # Reduce miss probability for high pos3 numbers

            ensemble_predictions[num] = np.clip(final_pred, 0.01, 0.99)
        else:
            ensemble_predictions[num] = 0.5

    return ensemble_predictions


@app.route("/predict-10-number", methods=["POST"])
def predict():
    try:
        data = request.json

        # Support multiple parameter names
        draws = data.get("draws") or data.get("draws_70") or data.get("history")

        if not draws or len(draws) < 30:
            return jsonify({
                "error": "Input must have at least 30 rounds",
                "received": len(draws) if draws else 0,
                "required": 30
            }), 400

        # Validate draws format
        for i, draw in enumerate(draws):
            if not isinstance(draw, list) or len(draw) == 0:
                return jsonify({
                    "error": f"Draw {i+1} is invalid. Each draw must be a list of numbers",
                    "received": str(draw)
                }), 400

        # Predict with ensemble
        try:
            preds = predict_with_ensemble(draws)
            prediction_source = "ensemble"
        except Exception as e:
            print(f"Ensemble prediction failed: {e}")
            return jsonify({"error": f"Prediction failed: {str(e)}"}), 500

        # Sort by highest miss probability
        top20 = sorted(preds.items(), key=lambda x: -x[1])[:20]
        top20_numbers = [i[0] for i in top20]

        return jsonify({
            "top20_miss_numbers": top20_numbers,
            "prediction_source": prediction_source,
            "total_draws": len(draws),
            "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S")
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/update-result", methods=["POST"])
def update_result():
    """API to update actual results for learning."""
    try:
        data = request.json

        actual_result = data.get("actual_result")
        draws = data.get("draws") or data.get("draws_70") or data.get("history")

        if not actual_result or not draws:
            return jsonify({
                "error": "Missing required fields: actual_result, draws"
            }), 400

        # Validate actual_result
        if not isinstance(actual_result, list) or len(actual_result) == 0:
            return jsonify({
                "error": "actual_result must be a non-empty list of numbers"
            }), 400

        # Get predictions
        try:
            predictions = predict_with_ensemble(draws)
        except Exception as e:
            return jsonify({
                "error": f"Could not recreate predictions: {str(e)}"
            }), 500

        # Calculate accuracy
        correct_predictions = 0
        for num in range(1, 81):
            predicted_miss = predictions.get(num, 0.5) > 0.5
            actual_miss = num not in actual_result
            if predicted_miss == actual_miss:
                correct_predictions += 1

        accuracy = correct_predictions / 80

        return jsonify({
            "message": "Result processed successfully",
            "accuracy": accuracy,
            "correct_predictions": correct_predictions,
            "total_predictions": 80
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/model-info", methods=["GET"])
def get_model_info():
    """API to get model information."""
    try:
        info = {
            "ensemble_models": list(ensemble_models.keys()) if ensemble_models else [],
            "total_features": len(feature_columns),
            "model_counts": {}
        }

        for strategy, models in ensemble_models.items():
            info["model_counts"][strategy] = len(models)

        return jsonify(info)

    except Exception as e:
        return jsonify({"error": str(e)}), 500


if __name__ == "__main__":
    print("Starting Keno Prediction API...")
    print(f"Available strategies: {list(ensemble_models.keys())}")
    print(f"Total features: {len(feature_columns)}")
    app.run(debug=True, port=5049)
