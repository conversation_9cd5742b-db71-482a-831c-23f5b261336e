import pandas as pd
import joblib
import lightgbm as lgb
from flask import Flask, request, jsonify
from feature_engineering import extract_features

app = Flask(__name__)


# Load trained models
models = joblib.load("multi_label_models.pkl")

@app.route("/predict-10-number", methods=["POST"])
def predict():
    try:
        data = request.json
        draws = data.get("draws_70")

        if not draws or len(draws) < 10:
            return jsonify({"error": "Input must have at least 30 rounds."}), 400

        # Convert input to features
        df_feat = extract_features(draws)
        X = df_feat.iloc[:, :240]  # Only 240 features (80 x 3), no labels

        # Predict probability of miss (label = 1 means miss)
        preds = {}
        for label, model in models.items():
            prob = model.predict_proba(X)[:, 1][0]  # single sample
            index = int(label.split("_")[-1])  # label_23 -> 23
            preds[index] = prob

        # Sort by highest miss prob
        top10 = sorted(preds.items(), key=lambda x: -x[1])[:20]
        top10_numbers = [i[0] for i in top10]

        return jsonify({"top10_miss_numbers": top10_numbers})

    except Exception as e:
        return jsonify({"error": str(e)})

if __name__ == "__main__":
    app.run(debug=True, port=5049)
