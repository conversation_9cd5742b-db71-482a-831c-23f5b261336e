<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Http\Request;

class BetManagerController extends Controller
{
    public function manager(Request $request)
    {
        // Lấy key đượ<PERSON> chọn (nếu có)
        $selectedKey = $request->get('key');
        
        // Lấy filter countMiss (mặc định >= 16) - CHỨC NĂNG MỚI
        $filterCountMiss = $request->get('filter_count_miss', 16);

        // Danh sách các key cache cho phép chọn (hardcode hoặc lấy từ config)
        $availableKeys = collect([
            'threee_15_dataBetNode',
            'threee_25_dataBetNode'
        ])->filter(fn($k) => str_contains($k, 'threee_') || str_contains($k, 'truot_'))->values();

        // Lấy dataBet từ cache (nếu key tồn tại) - LOGIC CŨ GIỮ NGUYÊN
        $dataBet = $selectedKey ? Cache::get($selectedKey, []) : [];

        // Tính summary (đếm số bộ theo mỗi countMiss) - LOGIC CŨ GIỮ NGUYÊN
        $summary = collect($dataBet)
            ->groupBy('countMiss')
            ->map(fn($items) => count($items))
            ->sortKeysDesc();

        return view('bet_manager', [
            'availableKeys' => $availableKeys,
            'selectedKey'   => $selectedKey,
            'dataBet'       => $dataBet, // GIỮ NGUYÊN - LOGIC CŨ
            'summary'       => $summary,
            'filterCountMiss' => $filterCountMiss, // THÊM MỚI
        ]);
    }

    public function update(Request $request)
    {
        $key = $request->get('key');
        $applyToAll = $request->get('apply_to_all', false); // CHỨC NĂNG MỚI - Apply to all
        
        if (!$key && !$applyToAll) {
            return redirect()->route('bet.manager')->with('error', 'Chưa chọn cache key.');
        }

        // CHỨC NĂNG MỚI - Lấy danh sách keys cần xử lý
        $keysToProcess = [];
        if ($applyToAll) {
            $keysToProcess = [
                'threee_15_dataBetNode',
                'threee_25_dataBetNode'
            ];
        } else {
            $keysToProcess = [$key]; // LOGIC CŨ - chỉ xử lý 1 key
        }

        // CHỨC NĂNG MỚI - Xử lý từng cache key
        foreach ($keysToProcess as $currentKey) {
            $this->processUpdateForKey($currentKey, $request);
        }

        $message = $applyToAll ? 'Cập nhật thành công cho tất cả cache' : 'Cập nhật thành công';
        
        return redirect()->route('bet.manager', ['key' => $key])
            ->with('success', $message);
    }

    private function processUpdateForKey($key, Request $request)
    {
        // Lấy data cũ
        $data = Cache::get($key, []);

        // ____________1. Xử lý bulk kill/add (nếu có) ____________
        $bulkKillValue = (int) $request->input('bulk_kill_value', 0);
        $bulkAddValue  = (int) $request->input('bulk_add_value', 0);

        // Nếu có chọn checkbox (selected[]) thì xử lý bulk
        $selectedIndexes = array_keys($request->input('selected', []));

        if (!empty($selectedIndexes)) {
            foreach ($selectedIndexes as $idx) {
                if (isset($data[$idx])) {
                    // Nếu có cả kill và add trong bulk, ưu tiên kill trước
                    if ($bulkKillValue > 0) {
                        $data[$idx]['countMiss'] = max(0, $data[$idx]['countMiss'] - $bulkKillValue);
                    }
                    if ($bulkAddValue > 0) {
                        $data[$idx]['countMiss'] = $data[$idx]['countMiss'] + $bulkAddValue;
                    }
                }
            }
        }

        // Reset các giá trị để không ảnh hưởng đến phần kill single / add single
        $requestKillValue = (int) $request->input('kill_value', 0);
        $requestAddValue  = (int) $request->input('add_value', 0);

        // ____________2. Xử lý kill_pairs (nếu có) ____________
        if ($request->filled('kill_pairs')) {
            $nums = collect(explode(',', $request->input('kill_pairs')))
                ->map(fn($n) => str_pad(trim($n), 2, '0', STR_PAD_LEFT))
                ->filter()
                ->toArray();

            $killPairValue = (int) $request->input('kill_pair_value', 0);
            if ($killPairValue > 0 && count($nums) > 0) {
                foreach ($data as $idx => &$item) {
                    // Nếu bộ số chứa ít nhất 1 trong kill_pairs
                    if (count(array_intersect($nums, $item['numberBet'])) > 0) {
                        $item['countMiss'] = max(0, $item['countMiss'] - $killPairValue);
                    }
                }
                unset($item);
            }
        }

        // ____________3. Xử lý kill single / add single (từng dòng) ____________
        if ($requestKillValue > 0 && !empty($selectedIndexes)) {
            foreach ($selectedIndexes as $idx) {
                if (isset($data[$idx])) {
                    $data[$idx]['countMiss'] = max(0, $data[$idx]['countMiss'] - $requestKillValue);
                }
            }
        }

        if ($requestAddValue > 0 && !empty($selectedIndexes)) {
            foreach ($selectedIndexes as $idx) {
                if (isset($data[$idx])) {
                    $data[$idx]['countMiss'] = $data[$idx]['countMiss'] + $requestAddValue;
                }
            }
        }

        // ____________4. Lưu lại cache ____________
        Cache::put($key, $data, 90000);
    }

    // CHỨC NĂNG MỚI - Preview countMiss cho kỳ tiếp theo (KHÔNG LƯU CACHE)
    public function preview(Request $request)
    {
        $key = $request->get('key');
        
        if (!$key) {
            return response()->json(['error' => 'Missing key'], 400);
        }

        // Lấy data từ cache - LOGIC CŨ
        $dataBet = Cache::get($key, []);
        
        if (empty($dataBet)) {
            return response()->json(['error' => 'No data in cache'], 400);
        }

        // Lấy kết quả kỳ mới từ API (giống logic truotXien4)
        try {
            $responseListResult = $this->currentListResultKenno();
            $resultPre = $responseListResult['resultRaw']; // kq ky trc
            
            // Parse kết quả
            $arrayResultPre = explode('|', $resultPre);
            
        } catch (\Exception $e) {
            return response()->json(['error' => 'Cannot get current result: ' . $e->getMessage()], 400);
        }
        
        // Tính toán preview countMiss theo CHÍNH XÁC logic truotXien4
        $previewData = [];
        $keyXien4 = 3; // key nếu bộ 3 số thì để 3 còn bộ 4 thì để 4
        
        foreach ($dataBet as $index => $itemDataBet) {
            $arrayBet = $itemDataBet['numberBet'];
            $arrayDiffKQ = array_diff($arrayBet, $arrayResultPre);
            
            $newCountMiss = $itemDataBet['countMiss'];
            $status = 'unchanged';
            
            if (count($arrayDiffKQ) != $keyXien4) { 
                // Toạch xiên 3 - LOGIC GIỐNG truotXien4
                $newCountMiss += 1;
                $status = 'miss_increased';
            } else { 
                // Trúng xiên 4 - LOGIC GIỐNG truotXien4
                $newCountMiss = 0;
                $status = 'hit_reset';
            }
            
            $previewData[] = [
                'index' => $index,
                'numberBet' => $itemDataBet['numberBet'],
                'oldCountMiss' => $itemDataBet['countMiss'],
                'newCountMiss' => $newCountMiss,
                'status' => $status,
                'change' => $newCountMiss - $itemDataBet['countMiss']
            ];
        }
        
        return response()->json([
            'success' => true,
            'previewData' => $previewData,
            'resultRaw' => $resultPre,
            'totalItems' => count($previewData)
        ]);
    }
    
    // CHỨC NĂNG MỚI - Lấy kết quả kỳ hiện tại (giống logic truotXien4)
    private function currentListResultKenno()
    {
        try {
            // TODO: Thay bằng logic thực tế lấy từ API của bạn
            // Ví dụ: $response = Http::get('your-api-endpoint');
            
            // Mock data với format đúng - BẠN CẦN THAY BẰNG API THỰC TẾ
            $mockResults = [
                '01|05|12|15|20|25|30|35|40|45|50|55|60|65|70|75|78|79|80',
                '02|07|11|18|22|28|33|38|42|47|52|57|62|67|72|77|03|08|13|23',
                '04|09|14|19|24|29|34|39|44|49|54|59|64|69|74|79|06|10|16|21'
            ];
            
            // Random pick một kết quả để demo
            $resultRaw = $mockResults[array_rand($mockResults)];
            
            return [
                'resultRaw' => $resultRaw,
                'id' => time(),
                'periodId' => date('YmdHi')
            ];
            
        } catch (\Exception $e) {
            // Fallback nếu API lỗi
            return [
                'resultRaw' => '01|05|12|15|20|25|30|35|40|45|50|55|60|65|70|75|78|79|80',
                'id' => time(),
                'periodId' => date('YmdHi')
            ];
        }
    }
}
