import os
import sys
import subprocess
from datetime import datetime

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n{'='*50}")
    print(f"STEP: {description}")
    print(f"COMMAND: {command}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print("SUCCESS!")
        if result.stdout:
            print("OUTPUT:")
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"ERROR: {e}")
        if e.stdout:
            print("STDOUT:")
            print(e.stdout)
        if e.stderr:
            print("STDERR:")
            print(e.stderr)
        return False

def check_file_exists(filename, description):
    """Check if a file exists."""
    if os.path.exists(filename):
        size = os.path.getsize(filename)
        print(f"✓ {description}: {filename} ({size:,} bytes)")
        return True
    else:
        print(f"✗ {description}: {filename} NOT FOUND")
        return False

def main():
    print("="*60)
    print("KENO PREDICTION SYSTEM - FULL TRAINING PIPELINE")
    print("="*60)
    print(f"Started at: {datetime.now()}")
    
    # Step 1: Generate training data
    print("\n" + "="*60)
    print("STEP 1: GENERATING TRAINING DATA")
    print("="*60)
    
    if not run_command("python data_generator.py", "Generate training data from database"):
        print("FAILED: Could not generate training data")
        return False
    
    # Check if training data was created
    if not check_file_exists("training_features.pkl", "Training data file"):
        print("FAILED: Training data file was not created")
        return False
    
    # Step 2: Train models
    print("\n" + "="*60)
    print("STEP 2: TRAINING ENSEMBLE MODELS")
    print("="*60)
    
    if not run_command("python model_trainer.py", "Train ensemble models"):
        print("FAILED: Could not train models")
        return False
    
    # Check if models were created
    files_to_check = [
        ("ensemble_models.pkl", "Ensemble models"),
        ("training_results.pkl", "Training results"),
        ("feature_columns.txt", "Feature columns")
    ]
    
    all_files_exist = True
    for filename, description in files_to_check:
        if not check_file_exists(filename, description):
            all_files_exist = False
    
    if not all_files_exist:
        print("FAILED: Some required files were not created")
        return False
    
    # Step 3: Test API
    print("\n" + "="*60)
    print("STEP 3: TESTING API")
    print("="*60)
    
    print("Starting API server for testing...")
    print("Note: API will start on port 5049")
    print("You can test with:")
    print("curl -X POST http://localhost:5049/predict-10-number \\")
    print('  -H "Content-Type: application/json" \\')
    print('  -d \'{"draws": [[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20], [...]]}\'')
    
    # Summary
    print("\n" + "="*60)
    print("TRAINING PIPELINE COMPLETED SUCCESSFULLY!")
    print("="*60)
    
    print("\nGenerated files:")
    for filename, description in files_to_check:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"  - {filename}: {size:,} bytes")
    
    print(f"\nCompleted at: {datetime.now()}")
    
    print("\nNext steps:")
    print("1. Run: python predict_api.py")
    print("2. Test API endpoints")
    print("3. Integrate with your application")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 ALL STEPS COMPLETED SUCCESSFULLY!")
        sys.exit(0)
    else:
        print("\n❌ PIPELINE FAILED!")
        sys.exit(1)
