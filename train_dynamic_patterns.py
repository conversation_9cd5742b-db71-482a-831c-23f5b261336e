import pandas as pd
import numpy as np
import pymysql
from tqdm import tqdm
import argparse
from dynamic_pattern_learning import DynamicPatternLearning
from enhanced_feature_engineering import extract_enhanced_features
import joblib
from datetime import datetime

# Database config
DB_CFG = dict(
    host="127.0.0.1",
    user="root",
    password="",
    db="lstx",
    charset="utf8mb4",
    cursorclass=pymysql.cursors.DictCursor
)


def load_draws_from_db():
    """Load dữ liệu từ database."""
    conn = pymysql.connect(**DB_CFG)
    cursor = conn.cursor()
    
    cursor.execute("""
        SELECT results, period 
        FROM history_kennos 
        ORDER BY period ASC
    """)
    rows = cursor.fetchall()
    conn.close()
    
    draws = []
    for row in rows:
        draw = list(map(int, row['results'].split(',')))
        draws.append(draw)
    
    return draws


def generate_dynamic_training_data(draws, window_size=70, max_samples=3000):
    """Tạo dữ liệu training cho dynamic pattern learning."""
    training_data = []
    
    print(f"Generating dynamic training data from {len(draws)} draws...")
    
    for i in tqdm(range(window_size, len(draws) - 1), desc="Processing"):
        # Lấy history và actual result
        hist_draws = draws[i - window_size:i]
        actual_result = draws[i]
        
        training_item = {
            'draws': hist_draws,
            'actual_result': actual_result
        }
        
        training_data.append(training_item)
        
        # Giới hạn số lượng để tránh quá tải
        if len(training_data) >= max_samples:
            break
    
    return training_data


def train_dynamic_system(training_data, test_ratio=0.2):
    """Training dynamic pattern learning system."""
    print(f"Training dynamic pattern system with {len(training_data)} samples...")
    
    # Chia train/test
    split_idx = int(len(training_data) * (1 - test_ratio))
    train_data = training_data[:split_idx]
    test_data = training_data[split_idx:]
    
    print(f"Train samples: {len(train_data)}")
    print(f"Test samples: {len(test_data)}")
    
    # Khởi tạo dynamic system
    dynamic_system = DynamicPatternLearning()
    
    # Training
    dynamic_system.train_ensemble_models(train_data)
    
    # Evaluation trên test set
    print("\nEvaluating on test set...")
    total_accuracy = 0
    strategy_accuracies = {strategy: [] for strategy in dynamic_system.model_ensemble.keys()}
    
    for i, test_item in enumerate(tqdm(test_data[:100], desc="Testing")):  # Test 100 samples
        draws = test_item['draws']
        actual_result = test_item['actual_result']
        
        try:
            # Dự đoán
            predictions = dynamic_system.predict_with_ensemble(draws)
            
            # Tính accuracy
            correct = 0
            for num in range(1, 81):
                predicted_miss = predictions.get(num, 0.5) > dynamic_system.adaptive_thresholds[num]
                actual_miss = num not in actual_result
                if predicted_miss == actual_miss:
                    correct += 1
            
            accuracy = correct / 80
            total_accuracy += accuracy
            
            # Update system với kết quả thực tế
            dynamic_system.update_with_result(draws, actual_result, predictions)
            
        except Exception as e:
            print(f"Error testing sample {i}: {e}")
            continue
    
    avg_accuracy = total_accuracy / min(100, len(test_data))
    print(f"\nAverage accuracy on test set: {avg_accuracy:.4f}")
    
    # Lưu system
    dynamic_system.save_patterns()
    
    # Lưu training results
    results = {
        'avg_accuracy': avg_accuracy,
        'train_samples': len(train_data),
        'test_samples': len(test_data),
        'strategies': list(dynamic_system.model_ensemble.keys()),
        'model_counts': {strategy: len(models) for strategy, models in dynamic_system.model_ensemble.items()},
        'timestamp': datetime.now().isoformat()
    }
    
    joblib.dump(results, 'dynamic_training_results.pkl')
    
    return dynamic_system, results


def analyze_patterns(draws, dynamic_system):
    """Phân tích patterns trong dữ liệu."""
    print("\nAnalyzing patterns...")
    
    # Lấy sample để phân tích
    sample_draws = draws[-70:] if len(draws) >= 70 else draws
    
    # Sequence patterns
    seq_patterns = dynamic_system.extract_sequence_pattern(sample_draws)
    
    # Hot/cold cycles
    hot_cold = dynamic_system.detect_hot_cold_cycles(sample_draws)
    
    # Frequency momentum
    freq_momentum = dynamic_system.calculate_frequency_momentum(sample_draws)
    
    # Tóm tắt insights
    print("\n=== PATTERN ANALYSIS ===")
    
    # Hot numbers
    hot_numbers = [num for num in range(1, 81) 
                  if hot_cold[num]['status'] == 'hot' and hot_cold[num]['intensity'] > 0.3]
    print(f"Hot numbers (high appearance): {hot_numbers[:15]}")
    
    # Cold numbers  
    cold_numbers = [num for num in range(1, 81) 
                   if hot_cold[num]['status'] == 'cold' and hot_cold[num]['intensity'] > 0.3]
    print(f"Cold numbers (low appearance): {cold_numbers[:15]}")
    
    # Increasing trend
    increasing = [num for num in range(1, 81) 
                 if freq_momentum[num]['trend_prediction'] == 'increasing' and freq_momentum[num]['strength'] > 0.1]
    print(f"Numbers with increasing trend: {increasing[:15]}")
    
    # Decreasing trend
    decreasing = [num for num in range(1, 81) 
                 if freq_momentum[num]['trend_prediction'] == 'decreasing' and freq_momentum[num]['strength'] > 0.1]
    print(f"Numbers with decreasing trend: {decreasing[:15]}")
    
    # High momentum numbers
    high_momentum = sorted([(num, seq_patterns[num]['momentum']) for num in range(1, 81)], 
                          key=lambda x: abs(x[1]), reverse=True)[:10]
    print(f"High momentum numbers: {[f'{num}({momentum:.3f})' for num, momentum in high_momentum]}")


def compare_with_baseline(draws, dynamic_system, window_size=70):
    """So sánh với baseline prediction."""
    print("\nComparing with baseline...")
    
    if len(draws) < window_size + 10:
        print("Not enough data for comparison")
        return
    
    # Test trên 20 samples cuối
    test_samples = 20
    dynamic_correct = 0
    baseline_correct = 0
    
    for i in range(len(draws) - test_samples, len(draws) - 1):
        hist_draws = draws[i - window_size:i]
        actual_result = draws[i]
        
        try:
            # Dynamic prediction
            dynamic_preds = dynamic_system.predict_with_ensemble(hist_draws)
            
            # Baseline prediction (simple frequency)
            baseline_preds = {}
            for num in range(1, 81):
                freq = sum(1 for draw in hist_draws[-20:] if num in draw) / 20
                baseline_preds[num] = 1 - freq  # Xác suất trượt = 1 - tần suất xuất hiện
            
            # Tính accuracy
            for num in range(1, 81):
                actual_miss = num not in actual_result
                
                dynamic_miss = dynamic_preds.get(num, 0.5) > dynamic_system.adaptive_thresholds[num]
                if dynamic_miss == actual_miss:
                    dynamic_correct += 1
                
                baseline_miss = baseline_preds.get(num, 0.5) > 0.5
                if baseline_miss == actual_miss:
                    baseline_correct += 1
                    
        except Exception as e:
            print(f"Error in comparison: {e}")
            continue
    
    total_predictions = test_samples * 80
    dynamic_accuracy = dynamic_correct / total_predictions
    baseline_accuracy = baseline_correct / total_predictions
    
    print(f"Dynamic system accuracy: {dynamic_accuracy:.4f}")
    print(f"Baseline accuracy: {baseline_accuracy:.4f}")
    print(f"Improvement: {((dynamic_accuracy - baseline_accuracy) / baseline_accuracy * 100):.2f}%")


def main():
    parser = argparse.ArgumentParser(description='Train Dynamic Pattern Learning System')
    parser.add_argument('--window', type=int, default=70, help='Window size for historical data')
    parser.add_argument('--samples', type=int, default=3000, help='Maximum training samples')
    parser.add_argument('--test-ratio', type=float, default=0.2, help='Test data ratio')
    parser.add_argument('--analyze', action='store_true', help='Run pattern analysis')
    parser.add_argument('--compare', action='store_true', help='Compare with baseline')
    
    args = parser.parse_args()
    
    print("=== DYNAMIC PATTERN LEARNING TRAINING ===")
    print(f"Window size: {args.window}")
    print(f"Max samples: {args.samples}")
    print(f"Test ratio: {args.test_ratio}")
    
    # Load data
    print("\nLoading data from database...")
    draws = load_draws_from_db()
    print(f"Loaded {len(draws)} draws")
    
    # Generate training data
    training_data = generate_dynamic_training_data(draws, args.window, args.samples)
    print(f"Generated {len(training_data)} training samples")
    
    # Train system
    dynamic_system, results = train_dynamic_system(training_data, args.test_ratio)
    
    print(f"\n=== TRAINING RESULTS ===")
    print(f"Average accuracy: {results['avg_accuracy']:.4f}")
    print(f"Strategies trained: {results['strategies']}")
    print(f"Model counts: {results['model_counts']}")
    
    # Pattern analysis
    if args.analyze:
        analyze_patterns(draws, dynamic_system)
    
    # Baseline comparison
    if args.compare:
        compare_with_baseline(draws, dynamic_system, args.window)
    
    print(f"\nTraining completed! Models saved to: {dynamic_system.model_dir}")
    print("You can now use the enhanced API with dynamic pattern learning.")


if __name__ == "__main__":
    main()
